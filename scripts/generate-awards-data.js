/* eslint-disable no-console */
const fs = require('fs');
const readCsv = require('../src/lib/csv');
const slugify = require('../src/lib/slugify');

const main = async () => {
	const rows = await readCsv({ csvFile: 'scripts/awards.csv' });

	const event = '2025Q2';

	const data = {
		event,
		meta: {
			description: '...',
		},
		winners: [],
		nominees: [],
	};

	for (const row of rows) {
		const name = row['Nominee full name']?.trim() || null;
		const uuid = name ? slugify(name) : null;
		const bio = row['Description / Bio for Awards site']?.trim() || null;
		const category = slugify(row.Category)?.replace(/-/g, '--') || null;
		const image = uuid ? `../images/${uuid}.png` : null;
		const email = row['Nominee email']?.trim() || null;
		const website = row['Website for Awards site']?.trim() || null;

		const nominee = {
			uuid,
			bio,
			category,
			event,
			image,
			name,
			email,
			website,
		};

		data.nominees.push(nominee);
	}

	fs.writeFileSync(`src/awards/awards-by-event/${event}.json`, JSON.stringify(data, null, 2));
};

main().catch((error) => {
	console.error('Error:', error);
	process.exit(1);
});
