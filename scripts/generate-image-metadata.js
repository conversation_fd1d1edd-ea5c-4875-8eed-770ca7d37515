const fs = require('fs');
const path = require('path');
const { imageSize } = require('image-size');

const IMAGES_DIR = path.join(__dirname, '../public/images');
const OUTPUT_PATH = path.join(__dirname, 'imageMeta.json');

function walkDir(dir, callback) {
	fs.readdirSync(dir).forEach((f) => {
		const dirPath = path.join(dir, f);
		const isDirectory = fs.statSync(dirPath).isDirectory();
		if (isDirectory) {
			walkDir(dirPath, callback);
		} else {
			callback(path.join(dir, f));
		}
	});
}

const imageMap = {};

walkDir(IMAGES_DIR, (filePath) => {
	const ext = path.extname(filePath);
	if (!['jfif', '.jpg', '.jpeg', '.png', '.webp', '.svg'].includes(ext)) return;

	const file = fs.readFileSync(filePath);

	const dimensions = ext === '.svg' ? {} : imageSize(file);

	const relativePath = filePath.replace(IMAGES_DIR, '').replace(/\\/g, '/');
	const src = `/images${relativePath}`;

	const parts = relativePath
		.split('/')
		.filter(Boolean)
		.map((p) => path.basename(p, path.extname(p)));

	const id = parts.join('-');

	imageMap[id] = {
		src,
		width: dimensions.width || null,
		height: dimensions.height || null,
		type: ext.slice(1),
	};
});

fs.writeFileSync(OUTPUT_PATH, JSON.stringify(imageMap, null, 2));
console.log(`✅ Image metadata written as object to ${OUTPUT_PATH}`);
