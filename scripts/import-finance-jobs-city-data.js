const fs = require('fs');
const formatPhoneNumber = require('../src/lib/formatPhoneNumber');
const readCsv = require('../src/lib/csv');
const slugify = require('../src/lib/slugify');

const DESCRIPTIONS_CSV_IN = '/home/<USER>/Downloads/company-descriptions.csv';
const ADDRESSES_CSV_IN = '/home/<USER>/Downloads/office-addresses.csv';

const IMG_MAP = {
	BlackRock: 'blackrock.png',
	'Bank of America': 'boa.png',
	'Capital One': 'capital-one.jpg',
	'<PERSON>': 'charles-schwab.jpg',
	Citigroup: 'citigroup.jpg',
	'<PERSON>': 'edward-jones.jpg',
	'Fidelity Investments': 'fidelity.jpeg',
	'Goldman Sachs': 'goldman-sachs.jpg',
	'Merrill Lynch': 'merrill-lynch.jpg',
	'Northwestern Mutual': 'northwestern.jpg',
	PNC: 'pnc.jpg',
	'<PERSON>': 'raymond-james.png',
	TIAA: 'tiaa.jpg',
	UBS: 'ubs.jpg',
	USAA: 'usaa.png',
	Vanguard: 'vanguard.png',
	'Wells Fargo': 'wells-fargo.jpg',
};

const main = async () => {
	const descriptions = await readCsv({ csvFile: DESCRIPTIONS_CSV_IN });
	const addresses = await readCsv({ csvFile: ADDRESSES_CSV_IN });

	const addressesMapped = addresses.map((address) => {
		const { phone: phoneRaw, zip: zipRaw } = address;
		let zip = String(zipRaw);

		if (zip.length < 5) zip = `0${zip}`;

		const phone = formatPhoneNumber(String(phoneRaw));

		return {
			...address,
			phone,
			zip,
		};
	});

	const addressesByCity = addressesMapped.reduce((acc, address) => {
		const { city } = address;
		const slug = slugify(city);

		const array = acc[slug] || [];
		array.push(address);
		acc[slug] = array;

		return acc;
	}, {});

	const entries = Object.entries(addressesByCity);

	for (const [slug, companies] of entries) {
		const { city } = companies[0];
		const data = { city, slug, companies };

		fs.writeFileSync(`/home/<USER>/Achievable/marketing/src/blog/finance-jobs-by-city/${slug}.json`, JSON.stringify(data));
	}

	const companies = descriptions.map((data) => {
		const { citation, company: companyName, description, jobBoardUrl } = data;
		const slug = slugify(companyName);

		const image = `../images/finance-companies/${IMG_MAP[companyName]}`;
		return {
			citation,
			companyName,
			description,
			image,
			jobBoardUrl,
			slug,
		};
	});

	for (const company of companies) {
		const { slug } = company;

		fs.writeFileSync(
			`/home/<USER>/Achievable/marketing/src/blog/finance-companies-by-name/${slug}.json`,
			JSON.stringify(company)
		);
	}
};

main();
