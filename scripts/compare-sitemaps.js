/* eslint-disable no-console */
const fs = require('fs');
const { parseStringPromise } = require('xml2js');

function normalizeUrl(urlString) {
	try {
		const url = new URL(urlString);
		const host = url.hostname.replace(/^www\./, '').toLowerCase();
		let { pathname } = url;
		if (!pathname.endsWith('/')) pathname += '/';

		return `${url.protocol}//${host}${pathname}`;
	} catch {
		console.warn('Invalid URL skipped:', urlString);
		return null;
	}
}

/** Extract and normalize URLs from sitemap XML file */
async function extractUrlsFromSitemap(filePath) {
	const xml = fs.readFileSync(filePath, 'utf-8');
	const parsed = await parseStringPromise(xml);
	const rawUrls = parsed.urlset.url.map((u) => u.loc[0]);

	const normalizedUrls = rawUrls.map(normalizeUrl).filter(Boolean); // remove nulls

	return new Set(normalizedUrls);
}

function difference(setA, setB) {
	return [...setA].filter((url) => !setB.has(url));
}

async function compareSitemaps(oldPath, newPath) {
	console.log(`📄 Comparing:\n• Old: ${oldPath}\n• New: ${newPath}\n`);

	const oldUrls = await extractUrlsFromSitemap(oldPath);
	const newUrls = await extractUrlsFromSitemap(newPath);

	const missingInNew = difference(oldUrls, newUrls);
	const addedInNew = difference(newUrls, oldUrls);

	console.log('🔍 Comparison Report:\n');

	console.log(`🛑 URLs missing in NEW sitemap (${missingInNew.length}):`);
	if (missingInNew.length) console.log(missingInNew.join('\n'));
	else console.log('✅ None');

	console.log(`\n🆕 URLs added in NEW sitemap (${addedInNew.length}):`);
	if (addedInNew.length) console.log(addedInNew.join('\n'));
	else console.log('✅ None');

	console.log('\n📊 Summary:');
	console.log(`• Total in OLD: ${oldUrls.size}`);
	console.log(`• Total in NEW: ${newUrls.size}`);
	console.log(`• Dropped: ${missingInNew.length}`);
	console.log(`• Added: ${addedInNew.length}`);
}

const oldSitemap = process.argv[2];
const newSitemap = process.argv[3];

if (!oldSitemap || !newSitemap) {
	console.info('Usage: curl -o scripts/old-sitemap.xml https://achievable.me/sitemap-manual.xml');
	console.info('Usage: curl -o scripts/new-sitemap.xml http://localhost:4000/sitemap.xml');
	console.info('Usage: node scripts/compare-sitemaps.js scripts/old-sitemap.xml scripts/new-sitemap.xml');
	process.exit(1);
}

compareSitemaps(oldSitemap, newSitemap);
