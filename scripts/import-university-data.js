const fs = require('fs');
const path = require('path');
const _fetch = require('isomorphic-fetch');
const readCsv = require('../src/lib/readCsv');
const getFileExt = require('../src/lib/getFileExt');
const slugify = require('../src/lib/slugify');
const US_STATE_MAP = require('../src/constants/usAlphaCodeToStateMap');

const GOOGLE_DRIVE_ID_REGEX = /d\/(.*)\/view/;
const CSV_IN = '/home/<USER>/Downloads/College Data Spreadsheet - 20221221 - College data.csv';

const saveGoogleDriveImage = async ({ imageUrl, slug }) => {
	const match = GOOGLE_DRIVE_ID_REGEX.exec(imageUrl);

	if (!match) throw new Error(`No id found for google drive link ${imageUrl}`);

	const id = match[1];
	console.log(`Fetching image for ${slug}`);
	const response = await _fetch(`https://drive.google.com/uc?export=download&id=${id}`);

	if (!response.ok) {
		console.log(`Failed to fetch image for ${slug} - status ${response.status}`);
	}

	const arrayBuffer = await response.arrayBuffer();
	const buffer = Buffer.from(arrayBuffer);
	const ext = await getFileExt({ buffer, slug });

	const fileName = `${slug}.${ext}`;
	const imageAbsolutePath = path.join('src/blog/images/universities', fileName);

	console.log(`Saving image for ${slug}`);
	fs.writeFileSync(imageAbsolutePath, buffer, (err) => {
		if (err) throw err;
	});
	console.log('Image saved');

	return { imageAbsolutePath };
};

const STATE_CODE = 'WY';

const main = async () => {
	const allRows = await readCsv({ csvFile: CSV_IN });
	const rows = allRows.filter((row) => row['State'] === STATE_CODE);

	console.log(`Importing data for state: ${STATE_CODE}`);

	const univeristies = [];
	for (const row of rows) {
		const schoolName = row['School Name'];
		const slug = slugify(schoolName);
		const acceptanceRate = Number(row['Acceptance Rate'].replace('%', ''));
		const satMath = Number(row['SAT Math']);
		const satReadingAndWriting = Number(row['SAT Reading and Writing']);
		const satComposite = Number(row['SAT Composite']);
		const actComposite = Number(row['ACT Composite']);
		const url = row['URL'];
		const stateCode = row['State'];
		const state = US_STATE_MAP[stateCode];
		const imageUrl = row['Image'];

		const { imageAbsolutePath } = await saveGoogleDriveImage({
			imageUrl,
			slug,
		});
		const imageRelativePath = imageAbsolutePath.replace('src/blog', '..'); //20230314JF: image path must be relative

		const univeristyData = {
			schoolName,
			slug,
			stateCode,
			state,
			satMath,
			satReadingAndWriting,
			satComposite,
			actComposite,
			acceptanceRate,
			url,
			image: imageRelativePath,
		};

		univeristies.push(univeristyData);
	}

	const universitiesByState = univeristies.reduce((acc, university) => {
		const { state } = university;
		const existing = acc[state];
		acc[state] = existing ? [...existing, university] : [university];

		return acc;
	}, {});

	for (const state in universitiesByState) {
		const universitiesInState = universitiesByState[state];
		const stateData = {
			state,
			universities: universitiesInState,
		};
		const slug = slugify(state);

		console.log(`Saving data for ${slug}`);
		fs.writeFileSync(path.join('src/blog/universities-by-state', `${slug}.json`), JSON.stringify(stateData));
	}
};

main();
