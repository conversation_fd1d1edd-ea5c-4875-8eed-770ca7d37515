const fs = require('fs');
const path = require('path');

const CONFIG = {
	tutors: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'directory-tutors',
		jsonBaseDir: 'src/data/directory/tutors/tutors-by-product',
		templateString: '{product}',
		outputBaseTemplate: 'src/data/directory/tutors/tutors-by-product/{product}',
	},
	counselors: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'directory-counselors',
		jsonBaseDir: 'src/data/directory/counselors/counselors-by-type',
		templateString: '{type}',
		outputBaseTemplate: 'src/data/directory/counselors/counselors-by-type/{type}',
	},
	awards: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'awards',
		jsonBaseDir: 'src/data/awards/awards-by-event',
		templateString: '{event}',
		outputBaseTemplate: 'src/data/awards/awards-by-event/{event}',
	},
	universities: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'blog-universities',
		jsonBaseDir: 'src/data/blog/universities-by-state',
		templateString: '{state}',
		outputBaseTemplate: 'src/data/blog/universities-by-state/{state}',
	},
	financeCompanies: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'blog-finance-companies',
		jsonBaseDir: 'src/data/blog/finance-companies-by-name',
		templateString: '{name}',
		outputBaseTemplate: 'src/data/blog/finance-companies-by-name/{name}',
	},
	topGraduateSchools: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'blog-graduate-schools',
		jsonBaseDir: 'src/data/blog/top-graduate-schools-by-program',
		templateString: '{program}',
		outputBaseTemplate: 'src/data/blog/top-graduate-schools-by-program/{program}',
	},
	cltColleges: {
		imageMetaPath: 'scripts/imageMeta.json',
		directoryPrefix: 'blog-clt-colleges',
		jsonBaseDir: 'src/data/blog/clt-colleges-by-state',
		templateString: '{state}',
		outputBaseTemplate: 'src/data/blog/clt-colleges-by-state/{state}',
	},
};

const PRODUCT_GROUP_MAP = {
	'finra-sie': 'finra',
	'finra-series-6': 'finra',
	'finra-series-7': 'finra',
	'finra-series-63': 'finra',
	'finra-series-65': 'finra',
	'finra-series-66': 'finra',
	'amc-8': 'amc',
	'amc-12': 'amc',
};

const { imageMetaPath, directoryPrefix, jsonBaseDir, templateString, outputBaseTemplate } = CONFIG.tutors;

const imageMeta = JSON.parse(fs.readFileSync(imageMetaPath, 'utf-8'));

const getImageMetadata = (key) => {
	const meta = imageMeta[key];
	if (meta) return meta;

	const keys = Object.keys(PRODUCT_GROUP_MAP);

	for (const groupKey of keys) {
		if (key.includes(groupKey)) {
			return imageMeta[key.replace(groupKey, PRODUCT_GROUP_MAP[groupKey])];
		}
	}

	return null;
};

function getAllJsonFiles(dirPath) {
	let results = [];
	const entries = fs.readdirSync(dirPath, { withFileTypes: true });

	for (const entry of entries) {
		const fullPath = path.join(dirPath, entry.name);
		if (entry.isDirectory()) {
			results = results.concat(getAllJsonFiles(fullPath));
		} else if (entry.isFile() && entry.name.endsWith('.json')) {
			results.push(fullPath);
		}
	}

	return results;
}

function extractSlugFromImagePath(imagePath) {
	const parts = imagePath.replace(/^(\.\.\/)+|^\/+/, '').split('/');
	const filename = parts[parts.length - 1].replace(/\.[^.]+$/, '');
	return filename;
}

function generateImageKey(imagePath) {
	const inferredSlug = extractSlugFromImagePath(imagePath);
	return `${directoryPrefix}-${inferredSlug}`;
}

function annotateImages(obj) {
	if (Array.isArray(obj)) {
		return obj.map(annotateImages);
	}

	if (typeof obj === 'object' && obj !== null) {
		const newObj = { ...obj };

		for (const key of Object.keys(newObj)) {
			if (key === 'image' && typeof newObj[key] === 'string') {
				const imageKey = generateImageKey(newObj[key]);
				console.log(`Looking for key: ${imageKey} (from path: ${newObj[key]})`);
				const meta = getImageMetadata(imageKey);
				if (meta) {
					newObj[key] = meta;
					console.log(`Found metadata for: ${imageKey}`);
				} else {
					console.warn(`Image metadata not found for key: ${imageKey}`);
				}
			} else {
				newObj[key] = annotateImages(newObj[key]);
			}
		}

		return newObj;
	}

	return obj;
}

function processAllJsonFiles() {
	const jsonFiles = getAllJsonFiles(jsonBaseDir);

	for (const jsonFile of jsonFiles) {
		const fileName = path.basename(jsonFile, '.json');
		const productSlug = fileName;

		const outputPath = path.join(outputBaseTemplate.replace(templateString, `${productSlug}.json`));
		const jsonData = JSON.parse(fs.readFileSync(jsonFile, 'utf-8'));
		const annotated = annotateImages(jsonData);

		fs.mkdirSync(path.dirname(outputPath), { recursive: true });
		fs.writeFileSync(outputPath, JSON.stringify(annotated, null, 2), 'utf-8');
		console.log(`✅ Annotated: ${productSlug} -> ${outputPath}`);
	}
}

processAllJsonFiles();
