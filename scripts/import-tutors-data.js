const fs = require('fs');
const path = require('path');
const readCsv = require('../src/lib/readCsv');
const optimizeImages = require('../src/lib/optimizeImages');
const saveGoogleDriveImage = require('../src/lib/saveGoogleDriveImage');
const slugify = require('../src/lib/slugify');

const COUNSELOR_PATH = 'src/directory/counselors/counselors-by-type';
const TUTORS_PATH = 'src/directory/tutors/tutors-by-product';

const undergraduateCounselorsDataPath = path.join(__dirname, '..', `${COUNSELOR_PATH}/undergraduate-counselors.json`);
const graduateCounselorsDataPath = path.join(__dirname, '..', `${COUNSELOR_PATH}/graduate-counselors.json`);
const actTutorsDataPath = path.join(__dirname, '..', `${TUTORS_PATH}/act.json`);
const satTutorsDataPath = path.join(__dirname, '..', `${TUTORS_PATH}/sat.json`);
const greTutorsDataPath = path.join(__dirname, '..', `${TUTORS_PATH}/gre.json`);
const gmatTutorsDataPath = path.join(__dirname, '..', `${TUTORS_PATH}/gmat.json`);

const undergraduateCounselorsData = require(undergraduateCounselorsDataPath);
const graduateCounselorsData = require(graduateCounselorsDataPath);
const actTutorsData = require(actTutorsDataPath);
const satTutorsData = require(satTutorsDataPath);
const greTutorsData = require(greTutorsDataPath);
const gmatTutorsData = require(gmatTutorsDataPath);

const allPartners = [
	...undergraduateCounselorsData.counselors,
	...graduateCounselorsData.counselors,
	...actTutorsData.tutors,
	...satTutorsData.tutors,
	...greTutorsData.tutors,
	...gmatTutorsData.tutors,
];
const allPartnersUuids = allPartners.map(({ uuid }) => uuid);

const DOWNLOAD_IMAGES_ENABLED = false;
const CSV_IN = '/home/<USER>/Downloads/Tutor directory tracker - 20230509 - College admissions consultants.csv';

const main = async () => {
	const rowsRaw = await readCsv({ csvFile: CSV_IN });
	const data = rowsRaw.reduce((acc, row) => {
		const {
			'Website name': name,
			'Page URL': website,
			'Description for the listing (max 350 characters)': bio,
			'Hourly rate for the listing': hourlyRateRaw,
			'Logo or founder headshot': imageUrl,
			'Tutor directory': directoryType,
			'College admissions consulting?': undergradCounselor,
			'Graduade school admissions consulting?': gradCounselor,
			'ACT Tutor?': actTutor,
			'SAT Tutor?': satTutor,
			'GRE tutor?': greTutor,
			'GMAT tutor?': gmatTutor,
		} = row;

		const types = [];
		if (undergradCounselor === 'Yes') types.push('undergraduate-counselors');
		if (gradCounselor === 'Yes') types.push('graduate-counselors');
		if (actTutor === 'Yes') types.push('act');
		if (satTutor === 'Yes') types.push('sat');
		if (greTutor === 'Yes') types.push('gre');
		if (gmatTutor) types.push('gmat');
		const isTutorOrCounselor = types.length > 0;
		const needsDirectoryEntry = directoryType === 'Not in any directory';

		if (needsDirectoryEntry && isTutorOrCounselor) {
			const uuid = slugify(name);

			if (allPartnersUuids.includes(uuid)) {
				console.log(uuid);
				return acc;
			}

			const hourlyRate = (() => {
				if (!hourlyRateRaw || hourlyRateRaw === 'Contact for Rate') return null;

				if (hourlyRateRaw > 0) return String(Math.floor(hourlyRateRaw));

				if (hourlyRateRaw.includes(' to ')) {
					const [lower, higher] = hourlyRateRaw.split(' to ');
					return `$${lower} - $${higher}/hr`;
				}

				return null;
			})();

			return acc.concat({
				uuid,
				bio,
				email: null,
				hourlyRate,
				imageUrl,
				name,
				phone: null,
				promoted: false,
				website,
				types,
			});
		}

		return acc;
	}, []);

	if (DOWNLOAD_IMAGES_ENABLED) {
		for (let i = 0; i < data.length; i += 1) {
			const entry = data[i];
			const { imageUrl, uuid } = entry;
			console.log('Item', i);
			await saveGoogleDriveImage({
				folderPath: 'scripts/temp-images',
				imageUrl,
				slug: uuid,
			});
		}

		await optimizeImages({ inputFolder: path.join('scripts', 'temp-images') });
	}

	const newUndergraduateCounselors = [];
	const newGraduateCounselors = [];
	const newActTutors = [];
	const newSatTutors = [];
	const newGreTutors = [];
	const newGmatTutors = [];
	for (const entry of data) {
		const { uuid, bio, email, hourlyRate, name, phone, promoted, website, types } = entry;
		const baseData = {
			uuid,
			bio,
			email,
			hourlyRate,
			name,
			phone,
			promoted,
			website,
		};

		const fileName = `${uuid}.webp`;
		let image;
		try {
			image = fs.readFileSync(path.join(__dirname, 'temp-images', 'optimized', fileName));
		} catch (e) {
			// NOP
		}

		const handleImage = ({ product, type, skipImageWrite = true }) => {
			if (!image || skipImageWrite) return;

			if (type === 'tutor') {
				fs.writeFileSync(path.join(__dirname, '..', `src/directory/tutors/images/${product}/${fileName}`), image);
				const imageRelPath = `../images/${product}/${fileName}`;
				return { imageRelPath };
			}

			fs.writeFileSync(path.join(__dirname, '..', `src/directory/counselors/images/${fileName}`), image);
			const imageRelPath = `../images/${fileName}`;
			return { imageRelPath };
		};

		for (const type of types) {
			const isUndergradCounselor = type === 'undergraduate-counselors';

			switch (type) {
				case 'graduate-counselors':
				case 'undergraduate-counselors': {
					const { imageRelPath } = handleImage({ type: 'counselor' });
					const data = { ...baseData, imageRelPath };

					if (isUndergradCounselor) {
						newUndergraduateCounselors.push(data);
						break;
					}

					newGraduateCounselors.push(data);
					break;
				}
				case 'act': {
					const { imageRelPath } = handleImage({ product: 'act', type: 'tutor' });
					newActTutors.push({ ...baseData, image: imageRelPath });
					break;
				}
				case 'sat': {
					const { imageRelPath } = handleImage({ product: 'sat', type: 'tutor' });
					newSatTutors.push({ ...baseData, image: imageRelPath });
					break;
				}
				case 'gre': {
					const { imageRelPath } = handleImage({ product: 'gre', type: 'tutor' });
					newGreTutors.push({ ...baseData, image: imageRelPath });
					break;
				}
				case 'gmat': {
					const { imageRelPath } = handleImage({ product: 'gmat', type: 'tutor' });
					newGmatTutors.push({ ...baseData, image: imageRelPath });
					break;
				}
				default:
					break;
			}
		}
	}

	undergraduateCounselorsData.counselors = undergraduateCounselorsData.counselors.concat(newUndergraduateCounselors);
	fs.writeFileSync(undergraduateCounselorsDataPath, JSON.stringify(undergraduateCounselorsData));

	graduateCounselorsData.counselors = graduateCounselorsData.counselors.concat(newGraduateCounselors);
	fs.writeFileSync(graduateCounselorsDataPath, JSON.stringify(graduateCounselorsData));

	actTutorsData.tutors = actTutorsData.tutors.concat(newActTutors);
	fs.writeFileSync(actTutorsDataPath, JSON.stringify(actTutorsData));

	satTutorsData.tutors = satTutorsData.tutors.concat(newSatTutors);
	fs.writeFileSync(satTutorsDataPath, JSON.stringify(satTutorsData));

	greTutorsData.tutors = greTutorsData.tutors.concat(newGreTutors);
	fs.writeFileSync(greTutorsDataPath, JSON.stringify(greTutorsData));

	gmatTutorsData.tutors = gmatTutorsData.tutors.concat(newGmatTutors);
	fs.writeFileSync(gmatTutorsDataPath, JSON.stringify(gmatTutorsData));
};

main();
