/* eslint-disable no-await-in-loop */
const path = require('path');
const { createObjectCsvWriter } = require('csv-writer');
const actTutorsJson = require('../../src/directory/tutors/tutors-by-product/act.json');
const sieTutorsJson = require('../../src/directory/tutors/tutors-by-product/finra-sie.json');
const greTutorsJson = require('../../src/directory/tutors/tutors-by-product/gre.json');
const undergradCounselorsJson = require('../../src/directory/counselors/counselors-by-type/undergraduate-counselors.json');
const gradCounselorsJson = require('../../src/directory/counselors/counselors-by-type/graduate-counselors.json');

const header = [
	{ id: 'name', title: 'Name' },
	{ id: 'email', title: 'Email' },
	{ id: 'url', title: 'URL' },
];

const data = [actTuto<PERSON><PERSON><PERSON>, sie<PERSON><PERSON><PERSON><PERSON><PERSON>, gre<PERSON><PERSON><PERSON><PERSON><PERSON>, undergrad<PERSON>ou<PERSON><PERSON><PERSON><PERSON><PERSON>, grad<PERSON>ou<PERSON>lors<PERSON><PERSON>];

const main = async () => {
	for (const json of data) {
		const { counselors, product, tutors, type } = json;
		const isCounselor = !!type;

		const filename = isCounselor ? type : `${product}-tutors`;
		const outputPath = path.join(__dirname, `${filename}.csv`);
		const csvWriter = createObjectCsvWriter({
			path: outputPath,
			header,
		});

		const partners = isCounselor ? counselors : tutors;
		for (const partner of partners) {
			const { email: emailRaw, name, phone, uuid, website } = partner;
			const email = (() => {
				if (emailRaw) return emailRaw;
				if (website) return website;
				if (phone) return phone;
				return '(none)';
			})();
			const slug = isCounselor ? type : `${product}-tutors`;
			const url = `https://achievable.me/directory/${slug}/${uuid}/`;

			const record = { name, email, url };

			await csvWriter.writeRecords([record]);
		}
	}
};

main();
