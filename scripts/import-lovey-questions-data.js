const fs = require('fs');
const readCsv = require('../src/lib/csv');

const CSV_IN = '/home/<USER>/Downloads/Survey result mapping data spreadsheet - Interest Questions data.csv';

const main = async () => {
	const rows = await readCsv({ csvFile: CSV_IN });

	const questionsData = [];
	for (const row of rows) {
		const { question, category } = row;

		const choices = [
			{
				choice: 'Definitely yes',
				score: 4,
			},
			{
				choice: 'Yes',
				score: 3,
			},
			{
				choice: 'No',
				score: 2,
			},
			{
				choice: 'Definitely no',
				score: 1,
			},
		];

		const data = {
			title: 'How interested are you in this activity?',
			question,
			type: 'interestType',
			choices,
			category,
		};

		questionsData.push(data);
	}

	fs.writeFileSync('/home/<USER>/Downloads/questions.js', JSON.stringify(questionsData));
};

main();
