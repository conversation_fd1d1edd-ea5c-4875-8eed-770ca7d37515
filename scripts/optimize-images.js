/* eslint-disable no-console */
// https://www.gatsbyjs.com/docs/preoptimizing-images/

const sharp = require('sharp');
const glob = require('glob');
const fs = require('fs-extra');
const path = require('path');

const [, , PATH] = process.argv;

if (!PATH) {
	console.error('Missing required arguments.');
	console.error('Usage: node optimize-images.js <PATH>');
	process.exit(1);
}

const SUPPORTED_EXTENSIONS = ['png', 'jpg', 'jpeg', 'webp', 'gif', 'jfif'];
const MAX_WIDTH = 476;
const QUALITY = 70;

fs.mkdirSync(path.join(PATH, 'optimized'), { recursive: true });

const main = async () => {
	const matches = glob.sync(`${PATH}/*.{${SUPPORTED_EXTENSIONS.join(',')}}`);

	await Promise.all(
		matches.map(async (match) => {
			const stream = sharp(match);
			const info = await stream.metadata();

			if (info.width < MAX_WIDTH) {
				return;
			}

			const name = path.basename(match, path.extname(match));
			const optimizedPath = path.join(PATH, 'optimized', `${name}.webp`);
			await stream.resize(MAX_WIDTH).webp({ quality: QUALITY }).toFile(optimizedPath);
		})
	);
};

main();
