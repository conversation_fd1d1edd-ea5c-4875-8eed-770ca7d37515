/* eslint-disable no-console */

// 20250523US: This script generates a map of google drive file ids to file names for a given folder.

const fs = require('fs');
const path = require('path');
const _fetch = require('isomorphic-fetch');
const minimist = require('minimist');

const args = minimist(process.argv.slice(2));
const { apiKey, folderId, outputPath } = args;

if (!apiKey || !folderId || !outputPath) {
	console.error('Missing required arguments.');
	console.error(
		'Usage: node map-gdrive-id-filename.js --apiKey=YOUR_API_KEY --folderId=YOUR_FOLDER_ID --outputPath=YOUR_OUTPUT_PATH'
	);
	process.exit(1);
}

const main = async () => {
	try {
		console.log('Fetching file list from Google Drive...');

		const allFiles = [];
		let nextPageToken = null;
		const query = encodeURIComponent(`'${folderId}' in parents`);

		do {
			const url = `https://www.googleapis.com/drive/v3/files?q=${query}&fields=nextPageToken,files(id,name)&pageSize=1000&key=${apiKey}${nextPageToken ? `&pageToken=${nextPageToken}` : ''}`;

			const response = await _fetch(url);

			if (!response.ok) {
				const errorText = await response.text();
				throw new Error(`Google Drive API error: ${response.status} ${response.statusText}\n${errorText}`);
			}

			const data = await response.json();
			const { files } = data;

			if (!files || files.length === 0) {
				console.log('No files found in the specified folder.');
				break;
			}

			allFiles.push(...files);
			nextPageToken = data.nextPageToken;
		} while (nextPageToken);

		const result = {};
		for (const file of allFiles) {
			result[file.id] = file.name;
		}

		if (!fs.existsSync(outputPath)) {
			fs.mkdirSync(outputPath, { recursive: true });
			console.log(`Created output directory: ${outputPath}`);
		}

		const outputFile = path.join(outputPath, `gdrive-map-${folderId}.json`);
		fs.writeFileSync(outputFile, JSON.stringify(result, null, 2));

		console.log(`File map saved to: ${outputFile}`);
	} catch (err) {
		console.error('Error:', err.message);
		process.exit(1);
	}
};

main();
