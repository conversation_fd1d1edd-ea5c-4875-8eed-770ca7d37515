const fs = require('fs');
const readCsv = require('../src/lib/csv');
const addOrdinalSuffix = require('../src/lib/addOrdinalSuffix');

const CSV_IN = '/home/<USER>/Downloads/College admissions deadlines research - 20230721 - data-for-import.csv';

function stripNonAlphanumeric(inputString) {
	const regex = /[^A-Za-z0-9\s-\/.,:!?'"()]+/g;
	const resultString = inputString.replace(regex, '');

	return resultString;
}

const addOrdinalSuffixes = (inputString) => {
	const regex = /\d+/g;

	const resultString = inputString.replace(regex, (match) => {
		const number = parseInt(match);
		const result = addOrdinalSuffix(number);
		return result;
	});

	return resultString;
};

const convertMultiline = (text) => {
	if (!text) return null;

	const isMultiline = text.includes('\n');

	if (!isMultiline) {
		const string = addOrdinalSuffixes(text.trim());
		return string;
	}

	const split = text.split('\n');
	const array = split.map((str) => {
		const trimmed = str.trim();
		const cleaned = stripNonAlphanumeric(trimmed);
		const result = addOrdinalSuffixes(cleaned);
		return result;
	});

	return array;
};

const main = async () => {
	const rows = await readCsv({ csvFile: CSV_IN });
	const data = rows.map((row) => {
		const {
			college,
			url,
			altUrl,
			earlyActionDeadline,
			earlyDecisionDeadline,
			regularDecisionDeadline,
			transferDecisionDeadline,
			extra,
		} = row;

		return {
			college,
			url: url || null,
			altUrl: altUrl || null,
			earlyActionDeadline: convertMultiline(earlyActionDeadline),
			earlyDecisionDeadline: convertMultiline(earlyDecisionDeadline),
			regularDecisionDeadline: convertMultiline(regularDecisionDeadline),
			transferDecisionDeadline: convertMultiline(transferDecisionDeadline),
			extra: extra || null,
		};
	});

	fs.writeFileSync('/home/<USER>/Downloads/college-deadlines-data.js', JSON.stringify(data));
};

main();
