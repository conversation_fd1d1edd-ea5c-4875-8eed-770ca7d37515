const fs = require('fs');
const path = require('path');
const readCsv = require('../src/lib/csv');
const saveGoogleDriveImage = require('../src/lib/saveGoogleDriveImage');
const slugify = require('../src/lib/slugify');

const CSV_IN = '/home/<USER>/Downloads/graduate-schools-data.csv';

const IMG_SLUG_EXT_MAP = fs.readdirSync('src/blog/images/graduate-schools/').reduce((acc, file) => {
	const [slug, ext] = file.split('.');
	acc[slug] = ext;
	return acc;
}, {});
const IMG_SLUGS = Object.keys(IMG_SLUG_EXT_MAP);

const main = async () => {
	const rows = await readCsv({ csvFile: CSV_IN });

	const data = [];
	for (let i = 0; i < rows.length; i += 1) {
		const { program, school, acceptanceRate, source, greVerbal, greQuant, gmat, url, imageUrl, citation, description } =
			rows[i];

		try {
			const imageSlug = `${slugify(school)}--${slugify(program)}`;
			let imageRelativePath;

			if (IMG_SLUGS.includes(imageSlug)) {
				const ext = IMG_SLUG_EXT_MAP[imageSlug];
				imageRelativePath = `../images/graduate-schools/${imageSlug}.${ext}`;
			} else {
				const { imageAbsolutePath } = await saveGoogleDriveImage({
					folderPath: 'src/blog/images/graduate-schools',
					imageUrl,
					slug: imageSlug,
				});
				const [_, ext] = imageAbsolutePath.split('.');
				IMG_SLUGS.push(imageSlug);
				IMG_SLUG_EXT_MAP[imageSlug] = ext;
				imageRelativePath = imageAbsolutePath.replace('src/blog', '..');
			}

			data.push({
				program: program.trim(),
				school: school.trim(),
				acceptanceRate: acceptanceRate.trim(),
				source: source.trim(),
				greVerbal: String(greVerbal),
				greQuant: String(greQuant),
				gmat: String(gmat),
				url: url.trim(),
				image: imageRelativePath,
				citation: citation.trim(),
				description: description.trim(),
			});
		} catch (e) {
			console.log(e);
			const rowNumber = i + 2;
			console.log(`Failed to save imageUrl (${imageUrl}) for row ${rowNumber}`);
			return;
		}
	}

	const schoolsByProgram = data.reduce((acc, o) => {
		const { program } = o;
		const slug = slugify(program);

		const array = acc[slug] || [];
		array.push(o);
		acc[slug] = array;

		return acc;
	}, {});

	for (const [program, schools] of Object.entries(schoolsByProgram)) {
		const data = { program: schools[0].program, slug: program, schools };
		fs.writeFileSync(path.join('src/blog/top-graduate-schools-by-program', `${program}.json`), JSON.stringify(data));
	}
};

main();
