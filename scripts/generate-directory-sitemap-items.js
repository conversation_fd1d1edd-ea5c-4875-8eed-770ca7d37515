const fs = require('fs');
const path = require('path');

const undergraduateCounselorsData = require('../src/directory/counselors/counselors-by-type/undergraduate-counselors.json');
const graduateCounselorsData = require('../src/directory/counselors/counselors-by-type/graduate-counselors.json');
const actTutorsData = require('../src/directory/tutors/tutors-by-product/act.json');
const amc8TutorsData = require('../src/directory/tutors/tutors-by-product/amc-8.json');
const amc12TutorsData = require('../src/directory/tutors/tutors-by-product/amc-12.json');
const finraSieTutorsData = require('../src/directory/tutors/tutors-by-product/finra-sie.json');
const finraSeries6TutorsData = require('../src/directory/tutors/tutors-by-product/finra-series-6.json');
const finraSeries7TutorsData = require('../src/directory/tutors/tutors-by-product/finra-series-7.json');
const finraSeries63TutorsData = require('../src/directory/tutors/tutors-by-product/finra-series-63.json');
const finraSeries65TutorsData = require('../src/directory/tutors/tutors-by-product/finra-series-65.json');
const finraSeries66TutorsData = require('../src/directory/tutors/tutors-by-product/finra-series-66.json');
// const gmatTutorsData = require('../src/directory/tutors/tutors-by-product/gmat.json');
const greTutorsData = require('../src/directory/tutors/tutors-by-product/gre.json');
// const satTutorsData = require('../src/directory/tutors/tutors-by-product/sat.json');
const usmleTutorsData = require('../src/directory/tutors/tutors-by-product/usmle-step-1.json');

const directoryData = [
	undergraduateCounselorsData,
	graduateCounselorsData,
	actTutorsData,
	amc8TutorsData,
	amc12TutorsData,
	finraSieTutorsData,
	finraSeries6TutorsData,
	finraSeries7TutorsData,
	finraSeries63TutorsData,
	finraSeries65TutorsData,
	finraSeries66TutorsData,
	// gmatTutorsData,
	greTutorsData,
	// satTutorsData,
	usmleTutorsData,
];

const getXml = (path) => `<url>
  <loc>https://achievable.me/directory/${path}</loc>
  <changefreq>daily</changefreq>
  <priority>0.7</priority>
</url>`;

const main = () => {
	const result = [];
	for (const data of directoryData) {
		const { product, type } = data;

		if (type) {
			const { counselors } = data;
			counselors.forEach(({ uuid }) => {
				const path = `${type}/${uuid}/`;
				result.push(getXml(path));
			});
		}

		if (product) {
			const { tutors } = data;
			tutors.forEach(({ uuid }) => {
				const path = `${product}-tutors/${uuid}/`;
				result.push(getXml(path));
			});
		}
	}

	const xml = result.join('\n');
	fs.writeFileSync(path.join(__dirname, 'directory.xml'), xml);
};

main();
