/* eslint-disable no-console */

// 20250523US: This script imports CLT colleges data from a CSV, generates JSON files for each state and moves the images to the correct directory.

const fs = require('fs');
const path = require('path');

const readCsv = require('../src/lib/readCsv');
const slugify = require('../src/lib/slugify');
const US_STATE_MAP = require('../src/constants/blog/usAlphaCodeToStateMap');

const CSV_PATH = 'scripts/temp/clt-colleges.csv';
const OLD_IMAGES_DIR = 'scripts/temp/clt-colleges-images-old/optimized';
const NEW_IMAGES_DIR = 'scripts/temp/clt-colleges-images-new/optimized';
const OLD_IMAGES_MAP = 'scripts/temp/old-updated-map.json';
const NEW_IMAGES_MAP = 'scripts/temp/new-updated-map.json';
const OUTPUT_IMAGES_DIR = 'src/blog/images/clt-colleges/';
const OUTPUT_JSON_DIR = 'src/blog/clt-colleges-by-state/';
const GOOGLE_DRIVE_ID_REGEX = /d\/(.*?)\/view/;

fs.mkdirSync(OUTPUT_IMAGES_DIR, { recursive: true });
fs.mkdirSync(OUTPUT_JSON_DIR, { recursive: true });

const oldMap = JSON.parse(fs.readFileSync(OLD_IMAGES_MAP, 'utf8'));
const newMap = JSON.parse(fs.readFileSync(NEW_IMAGES_MAP, 'utf8'));

const moveImageAndGetPath = ({ oldUrl, newUrl, slug }) => {
	if (!oldUrl && !newUrl) return null;

	const url = newUrl || oldUrl;
	const map = newUrl ? newMap : oldMap;
	const imageDir = newUrl ? NEW_IMAGES_DIR : OLD_IMAGES_DIR;

	const match = GOOGLE_DRIVE_ID_REGEX.exec(url);
	if (!match) throw new Error(`No id found for google drive link ${url}`);

	const id = match[1];

	const fileName = map[id];
	if (!fileName) {
		console.log(`No file name found for id ${id}`);
		return null;
	}

	const [name] = fileName.split('.');
	const oldFileName = `${name}.webp`;
	const newFileName = `${slug}.webp`;
	const oldPath = path.join(imageDir, oldFileName);
	const newPath = path.join(OUTPUT_IMAGES_DIR, newFileName);

	console.log(`Moving ${oldPath} to ${newPath}`);
	if (!fs.existsSync(oldPath)) {
		console.log(`File not found: ${oldPath}`);
		return null;
	}
	fs.copyFileSync(oldPath, newPath);

	return `../images/clt-colleges/${newFileName}`;
};

const main = async () => {
	const rows = await readCsv({ csvFile: CSV_PATH });
	const collegesByState = {};

	for (const row of rows) {
		const collegeName = row['School Name'];
		const slug = slugify(collegeName);
		const stateCode = row['State'];
		const state = US_STATE_MAP[stateCode];
		const acceptanceRate = Number(row['Acceptance Rate'].replace('%', ''));
		const satMath = Number(row['SAT Math']);
		const satReadingAndWriting = Number(row['SAT Reading and Writing']);
		const satComposite = Number(row['SAT Composite']);
		const actComposite = Number(row['ACT Composite']);
		const url = row['URL'];
		const citation = row['Image Citation - HTML'];
		const description = row['Description Paragraph'];

		const imageRelativePath = moveImageAndGetPath({
			oldUrl: row['OLD Image'],
			newUrl: row['NEW IMAGE'],
			slug,
		});

		const collegeData = {
			collegeName,
			slug,
			stateCode,
			state,
			acceptanceRate,
			satMath,
			satReadingAndWriting,
			satComposite,
			actComposite,
			url,
			image: imageRelativePath,
			citation,
			description,
		};

		if (!collegesByState[state]) {
			collegesByState[state] = { state, slug: slugify(state), colleges: [] };
		}

		collegesByState[state].colleges.push(collegeData);
	}

	const slugs = [];
	for (const state of Object.keys(collegesByState)) {
		const stateData = collegesByState[state];
		slugs.push(stateData.slug);
		const jsonPath = path.join(OUTPUT_JSON_DIR, `${stateData.slug}.json`);
		fs.writeFileSync(jsonPath, JSON.stringify(stateData, null, 2));
		console.log(`Saved: ${jsonPath}`);
	}

	console.log('State slugs: ', JSON.stringify(slugs.sort(), null, 2));
	console.log('Done');
};

main().catch((err) => {
	console.error('Script failed:', err);
});
