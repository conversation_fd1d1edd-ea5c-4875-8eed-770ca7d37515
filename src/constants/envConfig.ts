const ENV_VARS = {
	GRAPHQL_URL: process.env.NEXT_PUBLIC_GRAPHQL_URL,
	STAGE: process.env.NEXT_PUBLIC_STAGE,
	RECAPTCHA_SITE_KEY: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
	ROOT_DOMAIN: process.env.ROOT_DOMAIN,
};

const REQUIRED_ENV_VARS: (keyof typeof ENV_VARS)[] = ['GRAPHQL_URL', 'STAGE', 'RECAPTCHA_SITE_KEY', 'ROOT_DOMAIN'];

const missingEnvVars = REQUIRED_ENV_VARS.filter((key) => !ENV_VARS[key]);

if (missingEnvVars.length > 0) {
	throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

const envConfig = {
	...ENV_VARS,
	isProduction: ENV_VARS.STAGE === 'production',
};

export default envConfig;
