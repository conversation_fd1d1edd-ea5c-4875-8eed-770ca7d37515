// Define the shape of our environment configuration
interface EnvConfig {
	GRAPHQL_URL: string;
	STAGE: string;
	RECAPTCHA_SITE_KEY: string;
	ROOT_DOMAIN: string;
	isProduction: boolean;
}

// Helper function to get required environment variable
function getRequiredEnvVar(key: string, envValue: string | undefined): string {
	if (!envValue) {
		throw new Error(`Missing required environment variable: ${key}`);
	}
	return envValue;
}

const ENV_VARS = {
	GRAPHQL_URL: process.env.NEXT_PUBLIC_GRAPHQL_URL,
	STAGE: process.env.NEXT_PUBLIC_STAGE,
	RECAPTCHA_SITE_KEY: process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY,
	ROOT_DOMAIN: process.env.ROOT_DOMAIN,
} as const;

const REQUIRED_ENV_VARS: (keyof typeof ENV_VARS)[] = ['GRAPHQL_URL', 'STAGE', 'RECAPTCHA_SITE_KEY', 'ROOT_DOMAIN'];

// Validate all required environment variables exist
const missingEnvVars = REQUIRED_ENV_VARS.filter((key) => !ENV_VARS[key]);

if (missingEnvVars.length > 0) {
	throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
}

// Create the config object with proper typing
const envConfig: EnvConfig = {
	GRAPHQL_URL: getRequiredEnvVar('NEXT_PUBLIC_GRAPHQL_URL', ENV_VARS.GRAPHQL_URL),
	STAGE: getRequiredEnvVar('NEXT_PUBLIC_STAGE', ENV_VARS.STAGE),
	RECAPTCHA_SITE_KEY: getRequiredEnvVar('NEXT_PUBLIC_RECAPTCHA_SITE_KEY', ENV_VARS.RECAPTCHA_SITE_KEY),
	ROOT_DOMAIN: getRequiredEnvVar('ROOT_DOMAIN', ENV_VARS.ROOT_DOMAIN),
	isProduction: getRequiredEnvVar('NEXT_PUBLIC_STAGE', ENV_VARS.STAGE) === 'production',
};

export default envConfig;
