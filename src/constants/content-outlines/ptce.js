const PTCE_OUTLINE = [
	{
		title: '1. Medications',
		summary:
			'Provides foundational knowledge of medications, including generic and brand names, drug classifications, and regulatory categories. Explores drug interactions, contraindications, organ system-based therapies, dosage forms, and administration routes. Also emphasizes medication safety, side effects, storage, and stability.',
	},
	{
		title: '2. Patient safety and quality assurance',
		summary:
			'Introduces strategies to prevent medication errors, such as identifying look-alike/sound-alike drugs, following safe dispensing practices, and applying proper labeling techniques. Highlights the importance of error reporting systems, root cause analysis, and hygiene standards. Includes quality assurance processes like drug utilization reviews and outlines the technician\'s role in promoting safe and effective medication use.',
	},
	{
		title: '3. Order entry and processing',
		summary:
			'Details the key responsibilities involved in handling prescriptions, including intake, data entry, and interpretation of sig codes. Covers non-sterile compounding, pharmaceutical calculations, inventory control, and insurance processing. Stresses the importance of accuracy, efficiency, and compliance throughout the prescription workflow.',
	},
	{
		title: '4. Federal requirements',
		summary:
			'Explains the laws and regulations that govern pharmacy practice, focusing on drug safety, controlled substances, hazardous waste disposal, and HIPAA privacy protections. Discusses federal programs such as REMS, CMEA, and OBRA, as well as prescription monitoring, FDA recalls, and DEA reporting. Addresses labeling, storage, and documentation standards critical to regulatory compliance.',
	},
];

export default PTCE_OUTLINE;
