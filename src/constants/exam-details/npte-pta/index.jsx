import ThemeText from '@components/libraryV2/ThemeText';

import styles from './styles.module.scss';

const NPTE_EXAM_DETAILS = {
	title: (
		<>
			National Physical Therapy Exam <ThemeText>(NPTE)</ThemeText>
		</>
	),
	description: (
		<div>
			The NPTE (National Physical Therapy Exam) is administered by the FSBPT (The Federation of State Boards of Physical Therapy) and includes a PT (Physical Therapist) version and PTA (Physical Therapist Assistant) version, which slightly differ. Passing the NPTE is a huge step toward becoming a licensed physical therapist or physical therapist assistant. The exam ensures that each candidate has the proper knowledge to practice physical therapy and can adequately provide care to their patients.
		</div>
	),
	time: '5 hours (PT) / 4 hours (PTA)',
	questions: (
		<>
			<div>225 multiple-choice questions (PT)</div>
			<div>180 multiple-choice questions (PTA)</div>
		</>
	),
	questionsText: '225 (PT) or 180 (PTA) multiple-choice questions',
	examFee: '$485',
	href: 'https://www.fsbpt.org/',
	examHost: 'FSBPT',
	passingScore: '600+',
	extra: (
		<div className={styles.extra}>
			<div className={styles.paragraph}>The NPTE-PT and NPTE-PTA exams cover the same information, but to different depths. The NPTE-PT exam consists of five sections, each with 45 multiple-choice questions, while the NPTE-PTA exam consists of only four sections, each with 45 multiple-choice questions. The PT exam covers more extensive knowledge than the PTA exam, but the content of the sections is equivalent. </div>
		</div>
	),
};

export default NPTE_EXAM_DETAILS;
