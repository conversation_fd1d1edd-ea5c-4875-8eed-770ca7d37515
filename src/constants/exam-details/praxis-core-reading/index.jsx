import Link from '@components/libraryV2/Link';
import List from '@components/libraryV2/List';
import ListItem from '@components/libraryV2/ListItem';
import ThemeText from '@components/libraryV2/ThemeText';

import styles from './styles.module.scss';

const PRAXIS_EXAM_DETAILS = {
	title: (
		<>
			Praxis Reading <ThemeText>(Praxis)</ThemeText>
		</>
	),
	description: (
		<div>
			The Praxis Reading Exam is part of the Praxis Core, the first test you need to pass to become a certified teacher.
			The exam takes approximately 85 minutes and includes 56 multiple-choice questions. Average passing scores are
			typically between 160-184 but often vary by state. You can check out each state’s passing score{' '}
			<Link
				href='https://praxis.ets.org/state-requirements.html'
				target='_blank'
				rel='noopener noreferrer'
				theme='primary'
			>
				here
			</Link>
			.
		</div>
	),
	time: '85 minutes',
	questions: (
		<>
			<div>56 multiple choice questions</div>
			<div>3 sections</div>
		</>
	),
	questionsText: '56 multiple choice questions',
	examFee: '$90',
	href: 'https://www.ets.org/praxis/',
	examHost: 'ETS',
	passingScore: 'Average: 160-184 (varies by state)',
	extra: (
		<div className={styles.extra}>
			<div className={styles.paragraph}>
				The Praxis Reading Exam is a series of multiple-choice questions that evaluate the ability to comprehend,
				analyze, and evaluate written passages.
			</div>
			<List>
				<ListItem>Key Ideas and Details: 17-22 questions, 35% of exam</ListItem>
				<ListItem>Craft, Structure, and Language Skills: 14-19 questions, 30% of exam</ListItem>
				<ListItem>Integration of Knowledge and Ideas: 17-22 questions, 35% of exam</ListItem>
			</List>
			<div className={styles.paragraph}>
				In each of these sections, various writing samples taken from journals, newspapers, magazines, novels, and
				visuals such as maps and graphs are included. These are included as short passages, long passages, paired
				passages, and brief statements on various subjects and real-life situations. Each question refers solely to the
				written statements, and no outside knowledge is required. This test is an evaluation of comprehension and
				analysis of writing.
			</div>
		</div>
	),
};

export default PRAXIS_EXAM_DETAILS;
