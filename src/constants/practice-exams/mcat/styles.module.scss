.center {
	margin: 16px auto;
	text-align: center;
}

.sourceLink {
	font-size: 14px;
	margin-top: 0;
	font-style: italic;
}

.paragraphContainer {
	display: flex;
	flex-flow: column nowrap;
	gap: 12px;
	line-height: 1.5;

	p {
		margin: 0;
	}

	h3 {
		margin: 0;
		font-size: 16px;
		font-weight: 600;
	}

	ol {
		padding-left: 24px;
		margin: 0;
		list-style-type: upper-roman;
		list-style-position: inside;

		li {
			margin-bottom: 8px;
			line-height: 1.5;
		}

		li:last-child {
			margin-bottom: 0;
		}
	}
}

.divider {
	margin: 16px 0;
	border-color: #eaf1f4;
	border-color: var(--color-main--separator);
}
