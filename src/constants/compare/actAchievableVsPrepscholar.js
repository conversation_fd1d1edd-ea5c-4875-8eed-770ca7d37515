import COURSE_STATS from '@constants/courseStats';
import SEO_PRODUCT from '@constants/seoProduct';

const ACT_SEO = SEO_PRODUCT.act;
const ACT_STATS = COURSE_STATS.act;

const ACT_COMPARE = [
	{
		type: 'Score improvement guarantee',
		columns: [
			{
				company: 'achievable',
				included: '6+ point increase',
				isChecked: true,
			},
			{
				company: 'prepscholar',
				included: '4 point increase',
			},
		],
	},
	{
		type: 'Course access',
		columns: [
			{
				company: 'achievable',
				included: '12 months',
				isChecked: true,
			},
			{
				company: 'prepscholar',
				included: '12 months',
				isChecked: true,
			},
		],
	},
	{
		type: 'Free to try',
		columns: [
			{
				company: 'achievable',
				included: 'Entire first chapter',
				extra: '(no credit card required)',
				isChecked: true,
			},
			{
				company: 'prepscholar',
				included: 'Only 5 days',
				extra: '(credit card required)',
			},
		],
	},
	{
		type: 'Practice tests',
		columns: [
			{
				company: 'achievable',
				included: `${ACT_STATS.numPracticeExams}+ original exams`,
				isChecked: true,
			},
			{
				company: 'prepscholar',
				included: 'Only 1 exam',
			},
		],
	},
	{
		type: 'Per-topic standard flashcard quizzes',
		columns: [
			{
				company: 'achievable',
				included: true,
			},
			{
				company: 'prepscholar',
				included: true,
			},
		],
	},
	{
		type: 'Dynamic quizzes to test formula understanding',
		columns: [
			{
				company: 'achievable',
				included: true,
			},
			{
				company: 'prepscholar',
				included: false,
			},
		],
	},
	{
		type: 'Personalizes to your strengths and weaknesses',
		columns: [
			{
				company: 'achievable',
				included: true,
			},
			{
				company: 'prepscholar',
				included: false,
			},
		],
	},
	{
		type: 'Built for mobile',
		columns: [
			{
				company: 'achievable',
				included: true,
			},
			{
				company: 'prepscholar',
				included: false,
			},
		],
	},
	{
		type: 'Study planner',
		columns: [
			{
				company: 'achievable',
				included: true,
			},
			{
				company: 'prepscholar',
				included: true,
			},
		],
	},
	{
		type: 'Instructor hotline',
		columns: [
			{
				company: 'achievable',
				included: 'Average 60 minutes',
				extra: '(within 24 hours)',
				isChecked: true,
			},
			{
				company: 'prepscholar',
				included: 'Within 2 business days',
			},
		],
	},
	{
		type: 'Final price',
		columns: [
			{
				company: 'achievable',
				included: ACT_SEO.priceF,
				isChecked: true,
			},
			{
				company: 'prepscholar',
				included: '$397',
			},
		],
	},
];

export default ACT_COMPARE;
