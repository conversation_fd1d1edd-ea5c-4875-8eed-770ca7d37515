// 20250702CL: Use USMLE pictures as placeholder. Replace with actual PTCE image when available

import NextImage from 'next/image';

import ImageDecorationWrapper from '@components/libraryV2/ImageDecorationWrapper';

import usmleTextbookImg from '@images/exams/usmle-step-1/usmle-step-1-textbook--896.png';

const ImgPtceTextbook = ({ className, colorIndex, reverse }) => {
	return (
		<ImageDecorationWrapper colorIndex={colorIndex} reverse={reverse}>
			<NextImage
				alt="Screenshot of Achievable PTCE textbook"
				src={usmleTextbookImg}
				className={className}
				width={420} />
		</ImageDecorationWrapper>
	);
};

export default ImgPtceTextbook;
