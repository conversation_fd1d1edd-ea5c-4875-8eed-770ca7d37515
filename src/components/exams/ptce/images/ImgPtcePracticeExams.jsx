// 20250702CL: Use USMLE pictures as placeholder. Replace with actual PTCE image when available

import NextImage from 'next/image';

import ImageDecorationWrapper from '@components/libraryV2/ImageDecorationWrapper';

import usmlePracticeExamsImg from '@images/exams/usmle-step-1/usmle-step-1-dashboard--896.png';

const ImgPtcePracticeExams = ({ className, colorIndex, reverse }) => {
	return (
		<ImageDecorationWrapper colorIndex={colorIndex} reverse={reverse}>
			<NextImage
				alt="Screenshot of Achievable PTCE practice exams"
				src={usmlePracticeExamsImg}
				className={className}
				width={420} />
		</ImageDecorationWrapper>
	);
};

export default ImgPtcePracticeExams;
