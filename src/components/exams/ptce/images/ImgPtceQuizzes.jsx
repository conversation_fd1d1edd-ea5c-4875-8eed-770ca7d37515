// 20250702CL: Use USMLE pictures as placeholder. Replace with actual PTCE image when available

import NextImage from 'next/image';

import ImageDecorationWrapper from '@components/libraryV2/ImageDecorationWrapper';

import usmleQuizzesImg from '@images/exams/usmle-step-1/usmle-step-1-review-quiz--896.png';

const ImgPtceQuizzes = ({ className, colorIndex, reverse }) => {
	return (
		<ImageDecorationWrapper colorIndex={colorIndex} reverse={reverse}>
			<NextImage
				alt="Screenshot of Achievable PTCE quizzes"
				src={usmleQuizzesImg}
				className={className}
				width={420} />
		</ImageDecorationWrapper>
	);
};

export default ImgPtceQuizzes;
