import ImgPtceQuizzes from '@components/exams/ptce/images/ImgPtceQuizzes';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import ThemeText from '@components/libraryV2/ThemeText';

const ReviewQuizzes = () => {
	return (
		<ContentImageRow
			title={
				<>
					Review <ThemeText noWrap>quizzes</ThemeText>
				</>
			}
			titleSize='h2'
			titleTag='h2'
			image={<ImgPtceQuizzes colorIndex={2} reverse />}
			colorIndex={2}
			secondary
			tighten
		>
			Continually reviewing the material you've learned while studying is essential, but you need more than simple PTCE
			flashcards. In addition to our full-length practice exams, our PTCE study materials include a quiz bank of
			hundreds of review questions so you can quickly drill the key facts for any particular chapter.
		</ContentImageRow>
	);
};

export default ReviewQuizzes;
