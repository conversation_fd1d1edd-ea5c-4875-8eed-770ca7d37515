import ContentIconRow from '@components/libraryV2/ContentIconRow';
import ScoreTable from '@components/libraryV2/ScoreTable';
import ThemeText from '@components/libraryV2/ThemeText';

import styles from './styles.module.scss';

const PtcePassRates = () => {
	const passRateData = [
		{ Year: '2018', 'Exams Administered': '48,862', 'Exams Passed': '28,058', 'Pass Rate': '57%' },
		{ Year: '2019', 'Exams Administered': '51,768', 'Exams Passed': '29,910', 'Pass Rate': '58%' },
		{ Year: '2020', 'Exams Administered': '33,634', 'Exams Passed': '23,522', 'Pass Rate': '70%' },
		{ Year: '2021', 'Exams Administered': '42,615', 'Exams Passed': '30,134', 'Pass Rate': '71%' },
		{ Year: '2022', 'Exams Administered': '42,344', 'Exams Passed': '29,805', 'Pass Rate': '70%' },
	];

	return (
		<ContentIconRow
			iconClass='fa-duotone fa-chart-line'
			title={
				<>
					What is the <ThemeText>PTCE pass rate</ThemeText>?
				</>
			}
			reverseBackground
			withBackground
			secondary
			reverse
		>
			<div className={styles.content}>
				<p>
					The PTCE pass rate varies from year to year, but has remained around 70% over the past 3 years of available data. In 2024, the PTCE pass rate was also 70%.
				</p>
				<ScoreTable
					data={passRateData}
					className={styles.table}
				/>
			</div>
		</ContentIconRow>
	);
};

export default PtcePassRates;
