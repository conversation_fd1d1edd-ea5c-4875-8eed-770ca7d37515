
import Sidebar, { SidebarMobileMenu } from '@components/libraryV2/SidebarMenu';
import { CANONICAL_URLS } from '@constants/siteMetadata.ts';

const HEADING = 'PTCE';

const LINKS = [
	{
		to: CANONICAL_URLS.examsPtcePrepare,
		title: 'Prepare',
	},
	{
		to: CANONICAL_URLS.examsPtceExamInfo,
		title: 'Exam info',
	},
	{
		to: CANONICAL_URLS.examsPtceFreePracticeExam,
		title: 'Practice exam',
	},
	{
		to: CANONICAL_URLS.examsPtceFaq,
		title: 'FAQ',
	},
];

export const SidebarMobile = ({ currentPath }) => (
	<SidebarMobileMenu currentPath={currentPath} heading={HEADING} links={LINKS} />
);

export const SidebarDesktop = ({ children, currentPath }) => (
	<Sidebar currentPath={currentPath} heading={HEADING} links={LINKS}>
		{children}
	</Sidebar>
);
