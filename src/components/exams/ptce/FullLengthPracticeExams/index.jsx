import ImgPtcePracticeExams from '@components/exams/ptce/images/ImgPtcePracticeExams';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import ThemeText from '@components/libraryV2/ThemeText';

const FullLengthPracticeExams = () => {
	return (
		<ContentImageRow
			title={
				<>
					<ThemeText noWrap>Full-length</ThemeText> practice exams
				</>
			}
			titleSize='h2'
			titleTag='h2'
			image={<ImgPtcePracticeExams />}
			colorIndex={1}
			reverse
			secondary
			tighten
		>
			With hundreds of high-quality, hand-crafted PTCE practice exam questions, you can take full-length practice
			exams. Our PTCE practice questions include all the types of questions you'll see on the exam, and they're
			weighted according to the official PTCE exam rubric so that you can have confidence in your scores.
		</ContentImageRow>
	);
};

export default FullLengthPracticeExams;
