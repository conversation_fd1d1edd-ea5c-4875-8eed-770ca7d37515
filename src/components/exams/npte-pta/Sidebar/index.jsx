import React from 'react';

import Sidebar, { SidebarMobileMenu } from '@components/libraryV2/SidebarMenu';
import { CANONICAL_URLS } from '@constants/siteMetadata.ts';

const HEADING = 'NPTE-PTA';

const LINKS = [
	{
		to: CANONICAL_URLS.examsNptePtaPrepare,
		title: 'Prepare',
	},
	{
		to: CANONICAL_URLS.examsNptePtaExamInfo,
		title: 'Exam info',
	},
	{
		to: CANONICAL_URLS.examsNptePtaFaq,
		title: 'FAQ',
	},
];

export const SidebarMobile = ({ currentPath }) => (
	<SidebarMobileMenu currentPath={currentPath} heading={HEADING} links={LINKS} />
);

export const SidebarDesktop = ({ children, currentPath }) => (
	<Sidebar currentPath={currentPath} heading={HEADING} links={LINKS}>
		{children}
	</Sidebar>
);
