import ImgNptePtaQuizzes from '@components/exams/npte-pta/images/ImgNptePtaQuizzes';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import ThemeText from '@components/libraryV2/ThemeText';

const ReviewQuizzes = () => (
	<ContentImageRow
		image={<ImgNptePtaQuizzes colorIndex={2} reverse />}
		title={
			<>
				Review <ThemeText noWrap>quizzes</ThemeText>
			</>
		}
		titleSize='h2'
		titleTag='h2'
		secondary
		tighten
	>
		Continually reviewing the material you've learned while studying is essential, but you need more than simple
		flashcards. Our NPTE study materials include a quiz bank of hundreds of review questions so you can quickly drill
		the key facts for any particular chapter.
	</ContentImageRow>
);

export default ReviewQuizzes;
