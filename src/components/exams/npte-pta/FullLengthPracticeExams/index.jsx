import ImgNptePtaPracticeExams from '@components/exams/npte-pta/images/ImgNptePtaPracticeExams';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import ThemeText from '@components/libraryV2/ThemeText';

const FullLengthPracticeExams = () => (
	<ContentImageRow
		title={
			<>
				<ThemeText noWrap>Full-length</ThemeText> practice exams
			</>
		}
		titleSize='h2'
		titleTag='h2'
		image={<ImgNptePtaPracticeExams />}
		reverse
		secondary
		tighten
	>
		With hundreds of high-quality, hand-crafted NPTE practice exam questions, you can take full-length practice exams.
		Our NPTE practice questions include all the types of questions you'll see on the exam, and they're weighted
		according to the official NPTE exam rubric, so you can have confidence in your scores.
	</ContentImageRow>
);

export default FullLengthPracticeExams;
