import ImgPraxisReadingPracticeExams from '@components/exams/praxis-core-reading/images/ImgPraxisReadingPracticeExams';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import ThemeText from '@components/libraryV2/ThemeText';

const FullLengthPracticeExams = () => {
	return (
		<ContentImageRow
			title={
				<>
					<ThemeText noWrap>Full-length</ThemeText> practice exams
				</>
			}
			titleSize='h2'
			titleTag='h2'
			image={<ImgPraxisReadingPracticeExams />}
			reverse
			secondary
			tighten
		>
			With hundreds of high-quality hand-crafted Praxis Reading practice exam questions, you can take full-length
			practice exams. Our Praxis Reading practice questions include all the types of questions you'll see on the exam,
			and they're weighted according to the official Praxis Reading exam rubric so you can have confidence in your
			scores.
		</ContentImageRow>
	);
};

export default FullLengthPracticeExams;
