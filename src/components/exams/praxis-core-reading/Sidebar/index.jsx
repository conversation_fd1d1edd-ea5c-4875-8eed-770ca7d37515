import Sidebar, { SidebarMobileMenu } from '@components/libraryV2/SidebarMenu';

const HEADING = 'PRAXIS READING';

const LINKS = [
	{
		to: '/exams/praxis-core-reading/prepare/',
		title: 'Prepare',
	},
	{
		to: '/exams/praxis-core-reading/exam-info/',
		title: 'Exam info',
	},
	{
		to: '/exams/praxis-core-reading/faq/',
		title: 'FAQ',
	},
];

export const SidebarMobile = () => <SidebarMobileMenu heading={HEADING} links={LINKS} />;

export const SidebarDesktop = ({ children }) => (
	<Sidebar heading={HEADING} links={LINKS}>
		{children}
	</Sidebar>
);
