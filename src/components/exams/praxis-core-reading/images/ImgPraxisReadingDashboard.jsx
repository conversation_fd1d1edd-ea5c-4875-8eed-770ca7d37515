// 20250702CL: Use USMLE pictures as placeholder. Replace with actual Praxis Reading image when available
import NextImage from '@components/NextImage';
import ImageDecorationWrapper from '@components/libraryV2/ImageDecorationWrapper';

import usmleStep1DashboardImg from '@images/exams/usmle-step-1/usmle-step-1-dashboard--896.png';

const Image = ({ className, colorIndex, reverse }) => (
	<ImageDecorationWrapper colorIndex={colorIndex} reverse={reverse}>
		<NextImage
			alt='Screenshot of Achievable Praxis Reading dashboard'
			src={usmleStep1DashboardImg}
			className={className}
			width={420}
		/>
	</ImageDecorationWrapper>
);

export default Image;
