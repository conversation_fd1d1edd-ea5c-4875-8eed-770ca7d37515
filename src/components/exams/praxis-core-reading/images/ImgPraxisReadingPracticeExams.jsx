// 20250702CL: Use USMLE pictures as placeholder. Replace with actual Praxis Reading image when available
import NextImage from '@components/NextImage';
import ImageDecorationWrapper from '@components/libraryV2/ImageDecorationWrapper';

import usmleStep1TextbookImg from '@images/exams/usmle-step-1/usmle-step-1-textbook--896.png';

const Image = ({ className, colorIndex, reverse }) => (
	<ImageDecorationWrapper colorIndex={colorIndex} reverse={reverse}>
		<NextImage
			alt='Screenshot of Achievable Praxis Reading practice exams'
			src={usmleStep1TextbookImg}
			className={className}
			width={420}
		/>
	</ImageDecorationWrapper>
);

export default Image;
