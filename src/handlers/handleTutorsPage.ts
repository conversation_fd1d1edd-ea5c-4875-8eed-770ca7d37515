import { ProductKeyType } from '@/types/seo';
import { notFound } from 'next/navigation';

import { EXCLUDED_COURSES, LIMIT } from '@constants/directory';
import SEO_PRODUCT from '@constants/seoProduct';
import { getAllTutorProducts, getTutorProductData } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import stripHtml from '@lib/stripHtml';

import { getIsProfilePage, getPageNumber } from './shared';

const getTutorAndIndexByUuid = ({ tutors, uuid }: { tutors: any[]; uuid: string }) => {
	const index = tutors.findIndex((t) => t.uuid === uuid);
	const tutor = tutors[index];
	return { tutor, index };
};

export const generateTutorStaticParams = async () => {
	const allProducts = await getAllTutorProducts();
	const products = allProducts.filter((product) => !EXCLUDED_COURSES.includes(product));

	// 20250602US: Generate each product's first page at build time
	const params = products.map((product) => ({ slug: `${product}-tutors`, page: [] }));
	return params;
};

export const getIsTutorsDirectory = async ({ slug }: { slug: string }) => {
	const productSlug = slug.replace('-tutors', '');
	const tutorProductSlugs = await getAllTutorProducts();
	const isTutorsDirectory =
		productSlug && tutorProductSlugs.includes(productSlug) && !EXCLUDED_COURSES.includes(productSlug);
	return isTutorsDirectory;
};

export const generateTutorsPageMetadata = async ({
	productSlug,
	page = [],
}: {
	productSlug: ProductKeyType;
	page?: string[];
}) => {
	const isValidProductSlug = await getIsTutorsDirectory({ slug: productSlug });

	if (!isValidProductSlug) notFound();

	const { tutors } = await getTutorProductData(productSlug);
	const isProfilePage = getIsProfilePage(page);

	const seoProduct = SEO_PRODUCT[productSlug] || {};
	const { fullExamName = '', shortExamName = '' } = seoProduct;

	if (isProfilePage) {
		const [uuid] = page;
		const { tutor } = getTutorAndIndexByUuid({ tutors, uuid });

		if (!tutor) notFound();

		const { name, bio } = tutor;
		const description = `${stripHtml({ input: bio }).slice(0, 157)}...`;

		const tutorProfilePageMetadata = generatePageMetadata({
			title: `${name} ${fullExamName} tutoring`,
			keywords: [
				fullExamName,
				`${fullExamName} tutors`,
				`${fullExamName} tutoring services`,
				`${fullExamName} private tutors`,
				`${name} tutoring`,
			].join(', '),
			description,
			canonical: `/directory/${productSlug}-tutors/${uuid}`,
		});

		return tutorProfilePageMetadata;
	}

	const pageNumber = getPageNumber(page);
	const tutorListingPageMetadata = generatePageMetadata({
		title: `Private 1-on-1 ${shortExamName} tutors (${getCurrentYear()})`,
		keywords: `tutors, ${fullExamName} tutors`,
		description: `Discover top-rated ${shortExamName} tutors and take the first step towards acing the ${shortExamName}. Find the right fit for your personality and budget. Get personalized, 1-on-1 help to succeed.`,
		canonical: `/directory/${productSlug}-tutors/${pageNumber === 1 ? '' : pageNumber}`,
	});

	return tutorListingPageMetadata;
};

export const handleTutorPage = async ({ productSlug, page = [] }: { productSlug: ProductKeyType; page?: string[] }) => {
	const isValidProductSlug = productSlug && !EXCLUDED_COURSES.includes(productSlug);

	if (!isValidProductSlug) notFound();

	const { tutors, product, meta } = await getTutorProductData(productSlug);
	const isProfilePage = getIsProfilePage(page);

	if (isProfilePage) {
		const [uuid] = page;
		const { tutor, index } = getTutorAndIndexByUuid({ tutors, uuid });

		if (!tutor) notFound();

		const pageNumber = Math.floor(index / LIMIT) + 1;
		return { isProfilePage, pageNumber, product, tutor };
	}

	const pageNumber = getPageNumber(page);
	const totalPages = Math.ceil(tutors.length / LIMIT);

	if (pageNumber < 1 || pageNumber > totalPages) {
		notFound();
	}

	const basePath = `/directory/${productSlug}-tutors/`;
	const startIndex = (pageNumber - 1) * LIMIT;
	const paginatedTutors = tutors.slice(startIndex, startIndex + LIMIT);

	return {
		basePath,
		product,
		tutors: paginatedTutors,
		pageNumber,
		totalPages,
		meta,
	};
};
