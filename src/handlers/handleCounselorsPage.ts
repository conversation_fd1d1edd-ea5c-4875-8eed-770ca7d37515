import { notFound } from 'next/navigation';

import { LIMIT } from '@constants/directory';
import { getCounselorTypeData } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { capitalize } from '@lib/helpers';
import stripHtml from '@lib/stripHtml';

import { getIsProfilePage, getPageNumber } from './shared';

const COUNSELORS_PAGES = ['undergraduate-admissions-counselors', 'graduate-admissions-counselors'];

const getCounselorAndIndexByUuid = ({ counselors, uuid }: { counselors: any[]; uuid: string }) => {
	const index = counselors.findIndex((c) => c.uuid === uuid);
	const counselor = counselors[index];
	return { counselor, index };
};

export const getIsCounselorsDirectory = ({ slug }: { slug: string }) => {
	const isCounselorsDirectory = COUNSELORS_PAGES.includes(slug);
	return isCounselorsDirectory;
};

export const generateCounselorsPageMetadata = async ({
	typeSlug,
	page = [],
}: {
	typeSlug: string;
	page?: string[];
}) => {
	const isUndergradCounselors = typeSlug && typeSlug === 'undergraduate-admissions';
	const isGradCounselors = typeSlug && typeSlug === 'graduate-admissions';

	if (!isUndergradCounselors && !isGradCounselors) {
		notFound();
	}

	const { counselors, type } = await getCounselorTypeData(typeSlug);
	const isProfilePage = getIsProfilePage(page);
	const [level] = type.split('-');

	if (isProfilePage) {
		const [uuid] = page;
		const { counselor } = getCounselorAndIndexByUuid({ counselors, uuid });

		if (!counselor) notFound();

		const { name, bio } = counselor;
		const description = `${stripHtml({ input: bio }).slice(0, 157)}...`;
		const course = `${level} admissions`;

		return generatePageMetadata({
			title: `${name} ${course} counseling`,
			keywords: `${course}, ${course} counselors, ${course} counseling services, ${course} private counselors, ${name} counseling`,
			description,
			canonical: `/directory/${typeSlug}-counselors/${uuid}`,
		});
	}

	const pageNumber = getPageNumber(page);

	return generatePageMetadata({
		canonical: `/${typeSlug}-counselors/${pageNumber === 1 ? '' : pageNumber}`,
		description: `Get accepted by your dream school. Discover ${level} admissions counselors and former admissions officers. Craft the perfect application and outshine other candidates.`,
		keywords: `admissions, counselors, admissions counselors, ${level}, ${level} admissions counselors`,
		title: `${capitalize(level)} admissions counselors (${getCurrentYear()})`,
	});
};

export const handleCounselorPage = async ({ typeSlug, page = [] }: { typeSlug: string; page?: string[] }) => {
	const isUndergradCounselors = typeSlug && typeSlug === 'undergraduate-admissions';
	const isGradCounselors = typeSlug && typeSlug === 'graduate-admissions';

	if (!isUndergradCounselors && !isGradCounselors) {
		notFound();
	}

	const { counselors, type, meta } = await getCounselorTypeData(typeSlug);
	const isProfilePage = getIsProfilePage(page);

	if (isProfilePage) {
		const [uuid] = page;
		const { counselor, index: counselorIndex } = getCounselorAndIndexByUuid({ counselors, uuid });

		if (!counselor) notFound();

		const pageNumber = Math.floor(counselorIndex / LIMIT) + 1;
		const product = type.includes('undergraduate') ? 'act' : 'gre';

		return { counselor, isProfilePage, pageNumber, product, type };
	}

	const pageNumber = getPageNumber(page);
	const totalPages = Math.ceil(counselors.length / LIMIT);

	if (pageNumber < 1 || pageNumber > totalPages) {
		notFound();
	}

	const basePath = `/${typeSlug}-counselors/`;
	const startIndex = (pageNumber - 1) * LIMIT;
	const paginatedCounselors = counselors.slice(startIndex, startIndex + LIMIT);

	return {
		basePath,
		type,
		counselors: paginatedCounselors,
		pageNumber,
		slug: typeSlug,
		totalPages,
		meta,
	};
};
