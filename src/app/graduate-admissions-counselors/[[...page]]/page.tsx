import { notFound } from 'next/navigation';

import Counselors from '@components/directory/Counselors';

import { generateCounselorsPageMetadata, handleCounselorPage } from '@handlers/handleCounselorsPage';

export const revalidate = 86400;

interface Params {
	params: Promise<{ page?: string[] }>;
}

export async function generateMetadata({ params }: Params) {
	const { page } = await params;

	try {
		return await generateCounselorsPageMetadata({
			typeSlug: 'graduate-admissions',
			page,
		});
	} catch {
		return notFound();
	}
}

export function generateStaticParams() {
	return [
		{
			slug: 'graduate-admissions-counselors',
			page: [],
		},
	];
}

const Page = async ({ params }: Params) => {
	const { page } = await params;
	const counselorData = await handleCounselorPage({
		typeSlug: 'graduate-admissions',
		page,
	});
	const { basePath, counselors, totalPages, pageNumber, type } = counselorData;

	return (
		<Counselors basePath={basePath} type={type} counselors={counselors} page={pageNumber} totalPages={totalPages} />
	);
};

export default Page;
