import classNames from 'classnames';

import Layout from '@components/layout';
import BackgroundImageContainer from '@components/libraryV2/BackgroundImageContainer';
import Card from '@components/libraryV2/Card';
import ContactForm from '@components/libraryV2/ContactForm';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import ContentWell from '@components/libraryV2/ContentWell';
import Heading from '@components/libraryV2/Heading';
import Hero from '@components/libraryV2/Hero';
import LinkButton from '@components/libraryV2/LinkButton';
import ThemeText from '@components/libraryV2/ThemeText';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const seo = {
	canonical: CANONICAL_URLS.achievableForAll,
	description:
		"Unlock the full potential of your team with Achievable for all, offering exclusive, organization-wide access to Achievable's test preparation programs, supported by cutting-edge learning science, all for a single, unbeatable flat fee.",
	keywords: 'achievable, exam prep, test prep, organization test prep, finra, gre, act, usmle',
	title: 'Achievable for all',
};

export const metadata = generatePageMetadata(seo);

const schema = generateWebPageSchema(seo);

const SuccessIcon = () => <i className={classNames('fa-duotone fa-trophy-alt', styles.icon)} />;

const IncreaseIcon = () => <i className={classNames('fa-duotone fa-chart-line', styles.icon)} />;

const MoneyIcon = () => <i className={classNames('fa-duotone fa-sack-dollar', styles.icon)} />;

const SupportIcon = () => <i className={classNames('fa-duotone fa-hands-helping', styles.icon)} />;

const StudentIcon = () => <i className={classNames('fa-duotone fa-user-graduate', styles.icon)} />;

const Banner = () => (
	<div className={styles.banner}>
		<BackgroundImageContainer className={styles.background} theme='emerald' hideBackgroundMobile>
			<div className={styles.bannerContent}>
				<Card className={styles.card}>
					<i className={classNames('fa-solid fa-bullseye-arrow', styles.bullseye)} />
					<div className={styles.mission}>
						<ThemeText className={styles.emphasis} bold>
							Our mission
						</ThemeText>{' '}
						is to make high quality education materials accessible for all so that anyone can achieve their dreams.
					</div>
				</Card>
			</div>
		</BackgroundImageContainer>
	</div>
);

const AchievableForAll = () => (
	<Layout jsonLd={{ schemas: [schema] }} secondary>
		<Hero
			title={
				<>
					<ThemeText>Achievable</ThemeText> <span className={styles.noWrap}>for all</span>
				</>
			}
			description="Unlock the full potential of your team with Achievable for all, offering exclusive, organization-wide access to Achievable's test preparation programs, supported by cutting-edge learning science, all for a single, unbeatable flat fee."
			reverseBackground
			withBackground
		/>

		<Banner />

		<ContentImageRow
			image={<SuccessIcon />}
			title={
				<>
					Help your members reach their <ThemeText>full potential</ThemeText>
				</>
			}
			titleTag='h2'
		>
			<div>
				Achievable was founded to make it possible for anyone from any background to conquer the opportunity-gating
				exams standing between them and their goals. Whether your members are trying to get into their dream college,
				graduate school, or career, Achievable&apos;s modern exam preparation programs can help.
			</div>
			<LinkButton to='#contact'>Contact us</LinkButton>
		</ContentImageRow>

		<ContentImageRow
			image={<IncreaseIcon />}
			title={
				<>
					<ThemeText>Improve</ThemeText> admissions test scores
				</>
			}
			titleTag='h2'
			reverse
			secondary
			withBackground
		>
			Whether your members are applying to undergraduate universities or graduate programs, Achievable&apos;s prep
			programs guide your members to get the test scores they need. Better test scores are one of the best ways to
			improve admission chances.
		</ContentImageRow>

		<ContentImageRow
			image={<MoneyIcon />}
			title={
				<>
					Qualify for <ThemeText>lucrative careers</ThemeText>
				</>
			}
			titleTag='h2'
		>
			Desirable careers such as wealth management and medicine require that candidates complete licensure exams.
			Achievable&apos;s licensure exam prep programs have a 95%+ pass rate across all our exams.
		</ContentImageRow>

		<ContentImageRow
			image={<SupportIcon />}
			title={
				<>
					<ThemeText>Support</ThemeText> your members
				</>
			}
			titleTag='h2'
			reverse
			secondary
			withBackground
		>
			Our manager functionality gives organizations the tools they need to manage their members as they prepare for
			exams. See who is on target and who is falling behind at a glance, and diagnose troublesome areas with detailed
			analytics.
		</ContentImageRow>

		<ContentImageRow
			image={<StudentIcon />}
			title={
				<>
					Built for <ThemeText>all students</ThemeText>
				</>
			}
			titleTag='h2'
		>
			Our courses offer a personalized, guided path to success that can support any learner. Our content is easy to
			understand, our platform is easy to use, and is easily accessible on any device with an internet connection.
		</ContentImageRow>

		<ContentWell secondary withBackground>
			<Heading id='contact' size='h2' tagType='h2'>
				Contact <ThemeText>us</ThemeText>
			</Heading>
			<div className={styles.contactDetails}>
				Are you interested in learning more about partnering with us? We&apos;d love to hear from you. Please use the
				form below to get in touch with our team.
			</div>
			<ContactForm
				className={styles.contact}
				fields={{
					message: { label: 'How can we help you?' },
					name: true,
					organizationName: true,
				}}
			/>
		</ContentWell>
	</Layout>
);

export default AchievableForAll;
