.noWrap {
	white-space: nowrap;
}

.banner {
	padding: 80px 0;
	margin: 0 auto;
	width: 100%;
	max-width: 640px;
}

.background {
	border-radius: 8px;
	overflow: hidden;
}

.bannerContent {
	padding: 40px;
	border-radius: 4px;
}

.card {
	position: relative;
	overflow: hidden;
}

.mission {
	position: relative;
	padding: 20px;
	font-size: 18px;
	line-height: 24px;
}

.emphasis {
	font-size: 24px;
}

.bullseye {
	position: absolute;
	top: -16px;
	left: -24px;
	color: #f2f7fa;
	color: var(--color-main--light);
	font-size: 160px;
}

.content {
	display: flex;
	flex-flow: column nowrap;
	gap: 16px;
}

.icon {
	font-size: 160px;
	color: #15a5df;
}

.contactDetails {
	margin-top: 16px;
	margin-bottom: 32px;
	color: #525962;
	color: var(--color-main--secondary);
}

.contact {
	max-width: 640px;
	margin: 0 auto;
}

@media (max-width: 499px) {
	.banner {
		padding: 24px 0;
	}

	.emphasis {
		font-size: 22px;
	}

	.icon {
		font-size: 120px;
	}

	.contactDetails {
		margin: 16px 0;
	}
}
