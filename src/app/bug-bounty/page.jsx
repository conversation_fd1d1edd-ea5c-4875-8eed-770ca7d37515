import classNames from 'classnames';

import Layout from '@components/layout';
import Card from '@components/libraryV2/Card';
import ContentWell from '@components/libraryV2/ContentWell';
import Heading from '@components/libraryV2/Heading';
import Hero from '@components/libraryV2/Hero';
import ImageCard from '@components/libraryV2/ImageCard';
import Link from '@components/libraryV2/Link';
import List from '@components/libraryV2/List';
import ListCard from '@components/libraryV2/ListCard';
import Sticker from '@components/libraryV2/Sticker';
import ThemeText from '@components/libraryV2/ThemeText';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

import styles from './styles.module.scss';

const ELIGIBILE_EXAMPLES = [
	'Cross-site scripting',
	'Cross-site request forgery in a privileged context',
	'Server-side code execution',
	'Authentication or authorization flaws',
	'Stored injection vulnerabilities',
	'Directory traversal',
	'Information disclosure',
	'Significant infrastructure misconfiguration',
];

const INELIGIBILE_EXAMPLES = [
	'Content spoofing or text injection',
	'Self-cross-site scripting (self-XSS)',
	'Minimal-risk open redirects',
	'Minimal-risk XSRF such as sign out',
	'Minimal-risk clickjacking or UI redressing',
	'Missing HTTP request headers',
	'Missing cookie flags on non-sensitive cookies',
	'URL data sent to trusted analytics partners',
	'Link expirations such as password reset',
	'Persisted authentication after password change',
	'Password complexity, re-use, or related policies',
	'Email verification or account recovery policies',
	'Non-bulk username or email enumeration',
	'Unauthenticated access to cached content',
	'GraphQL or API endpoint enumeration',
	'Policies on rate-limiting or throttling',
	'DoS or DDoS attacks',
	'Invalid or missing SPF/DKIM/DMARC configuration',
	'SSL/TLS configuration best practices',
	'Host or software version disclosure',
	'Methods to bypass content metering',
	'Cross-site tracing (XST)',
	'Existence of EXIF data',
	'WordPress configuration (blog.achievable.me)',
	'Discourse configuration (talk.achievable.me)',
];

const TERMS = [
	'You give us a reasonable amount of time to investigate and fix any issues before publicly disclosing any information',
	'You make a good faith effort to avoid disruption to others, not conducting activities that lead to data deletion, data manipulation, or the degradation of our services',
	'You do not exploit any issue you discover',
	'You do not violate any laws or regulations',
];

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.bugBounty,
	description: "About Achievable's bug bounty program and ethical disclosure of security concerns.",
	keywords: 'achievable, bug bounty, bug bounty program, security program',
	title: 'Bug bounty program',
});

const Page = () => (
	<Layout>
		<Hero
			title={
				<div>
					<ThemeText>Bug Bounty</ThemeText> <div>Program</div>
				</div>
			}
			description='Get rewarded for reporting security concerns'
			withBackground
		/>

		<ContentWell tighten>
			<Card className={styles.section}>
				<Heading size='h2' tagType='h2'>
					<ThemeText>Bug bounty</ThemeText> and security program overview
				</Heading>
				<div>
					We take security very seriously at Achievable. If you believe you&apos;ve found a security issue on
					Achievable, please let us know as soon as possible. We will investigate all legitimate reports and fix any
					issues.
				</div>
				<div>
					We have given out rewards for reported issues on a case-by-case basis. Rewards vary depending on the security
					of the issue and are typically within $50 to $500 USD.
				</div>
				<Card className={styles.bold}>
					We encourage all valid reports. However, if you are simply going to run a minimal-effort scanner (e.g. Burp
					Suite or ZAP), please do not. We receive countless inelegible, non-exploitable &quot;issues&quot; from these
					scanners and will not reply; you will just be wasting your time and ours. Thank you.
				</Card>
				<div>
					Please also note that our access token system intentionally supports authenticating across multiple devices at
					the same time. If a user has signed into multiple devices, and then signs out of one device, the others should
					remain signed in. The persistence of access tokens is necessary to support this use case, and any request made
					using a valid access token from any device should succeed.
				</div>
			</Card>
		</ContentWell>

		<ContentWell secondary tighten>
			<div className={styles.section}>
				<Heading size='h2' tagType='h2'>
					How to submit an <ThemeText>issue report</ThemeText>
				</Heading>
				<div>
					Submit your issue report via email to{' '}
					<Link theme='primary' href='mailto:<EMAIL>'>
						<EMAIL>
					</Link>
					, including clear and concise reproduction instructions.
				</div>
				<div>
					Your report will be investigated by a member of our security team as soon as reasonably possible. We may
					contact you to request additional information. If your report is determined to be a valid issue and you are
					the first reporter, we will assign a reward based on the severity and send payment via PayPal.
				</div>
				<Sticker iconClass='fa-regular fa-circle-info' bold>
					Rewards will ONLY be made via PayPal.
				</Sticker>
			</div>
		</ContentWell>

		<ContentWell reverseBackground withBackground tighten>
			<div className={classNames(styles.section, styles.eligibility)}>
				<Heading size='h2' tagType='h2'>
					<ThemeText>Issue</ThemeText> eligibility
				</Heading>
				<div>
					We encourage you to ethically disclose vulnerabilities to us so we have the opportunity to address any issues
					and coordinate disclosure after a fix has been deployed. All reports regarding Achievable&apos;s security are
					welcomed, provided that the issue is exploitable by an adversary. Please check the lists below for a list of
					common eligible examples, as well as common non-exploitable &quot;issues&quot; which do not qualify for a
					reward.
				</div>
				<ListCard title='Eligible examples' list={ELIGIBILE_EXAMPLES} />
				<ListCard title='Ineligible examples' list={INELIGIBILE_EXAMPLES} />
			</div>
		</ContentWell>

		<ContentWell secondary tighten>
			<div className={styles.terms}>
				<ImageCard className={styles.termsIcon} type='document' rounded />
				<div className={styles.termsContent}>
					<Heading size='h2' tagType='h2'>
						<ThemeText>Terms</ThemeText> and conditions
					</Heading>
					<div>
						<div>We promote ethical disclosure and ask that:</div>
						<List className={styles.termsList}>
							{TERMS.map((term) => (
								<li key={term}>{term}</li>
							))}
						</List>
						<div className={styles.footer}>
							<div>Thank you!</div>
							<Link theme='primary' className={styles.emailLink} href='mailto:<EMAIL>' target='_blank'>
								Achievable security team
							</Link>
						</div>
					</div>
				</div>
			</div>
		</ContentWell>
	</Layout>
);

export default Page;
