.section {
	display: flex;
	flex-flow: column nowrap;
	align-items: flex-start;
	gap: 24px;
}

.bold {
	font-weight: 700;
}

.eligibility {
	position: relative;
	z-index: 1;
}

.terms {
	display: flex;
	flex-flow: row nowrap;
	justify-content: center;
	align-items: center;
	gap: 64px;
}

.termsIcon {
	height: 200px;
	width: 200px;

	svg {
		height: 140px;
		width: 140px;
	}
}

.termsContent {
	display: flex;
	flex-flow: column nowrap;
	gap: 24px;
	max-width: 520px;
}

.termsList {
	padding: 16px;
}

.footer {
	display: flex;
	flex-flow: column nowrap;
	gap: 16px;
}

@media (max-width: 599px) {
	.terms {
		flex-flow: column nowrap;
		align-items: flex-start;
		gap: 24px;
	}

	.termsIcon {
		height: unset;
		width: unset;

		svg {
			height: unset;
			width: unset;
		}
	}
}
