import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/praxis-core-reading/Sidebar';
import Layout from '@components/layout';
import ExamOverview from '@components/libraryV2/ExamOverview';
import HeroStretch from '@components/libraryV2/HeroStretch';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SEO = SEO_PRODUCT['praxis-core-reading'];
const PRAXIS_READING_SEO = { ...SEO, canonical: CANONICAL_URLS.examsPraxisReadingExamInfo };
const { title, description, keywords, canonical, sku, imageUrl } = PRAXIS_READING_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>Praxis Reading</ThemeText> <div>Exam information</div>
					</>
				}
				titleTag='h1'
				description="What it is, what's tested, and how it's scored."
			/>

			<SidebarDesktop>
				<ExamOverview seoProduct={PRAXIS_READING_SEO} outlineSecondary={false} summarySecondary={true} hideCta />

				<PassGuaranteedCta
					exam={PRAXIS_READING_SEO.shortExamName}
					href={PRAXIS_READING_SEO.firstPage}
					noGuarantee={!PRAXIS_READING_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
