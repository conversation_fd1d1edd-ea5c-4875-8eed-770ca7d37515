import TrackProduct from '@components/TrackProduct';
import FullLengthPracticeExams from '@components/exams/praxis-core-reading/FullLengthPracticeExams';
import { SidebarDesktop, SidebarMobile } from '@components/exams/praxis-core-reading/Sidebar';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const PRAXIS_READING_SEO = SEO_PRODUCT['praxis-core-reading'];
const { title, description, keywords, canonical, sku, imageUrl } = PRAXIS_READING_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<OverviewHero
				product={PRAXIS_READING_SEO.sku}
				title={
					<>
						Pass the <ThemeText noWrap>Praxis Reading</ThemeText>
					</>
				}
				description={
					<div className={styles.description}>
						<div>
							The Praxis Reading Exam is part of the Praxis Core, the first test you need to pass to become a certified
							teacher. Achievable is the best and most effective teacher prep on the market and the only Praxis Reading
							prep course that uses memory science-enhanced practice to ensure you reach your goal and become a
							certified teacher in less time.
						</div>
						<div>
							Achievable exam prep includes our online textbook, review questions, and full-length practice exams, all
							of which are easily accessible on your phone, tablet, or computer.
						</div>
					</div>
				}
				tag='PRAXIS READING EXAM PREP'
			/>

			<SidebarDesktop>
				<Reviews />

				<IncludesEverything
					course={PRAXIS_READING_SEO.shortExamName}
					href={PRAXIS_READING_SEO.offerUrl}
					price={PRAXIS_READING_SEO.priceF}
					product={PRAXIS_READING_SEO.sku}
				/>

				<FeatureSections>
					<AdvancedPersonalization product={PRAXIS_READING_SEO.sku} secondary>
						Achievable Praxis Reading exam prep uses adaptive learning techniques to create and update a personalized
						model of your memory, individually tracking your retention and mastery of each Praxis Reading learning
						objective. Our learning engine monitors your study progress and continually adjusts your quiz questions to
						ensure you focus on the topics that matter most, improving study effectiveness and reducing overall study
						time.
					</AdvancedPersonalization>

					<FullLengthPracticeExams />

					<ReviewQuizzes colorIndex={2} product={PRAXIS_READING_SEO.sku} reverseImage secondary />

					<OnlineTextbook product={PRAXIS_READING_SEO.sku} colorIndex={3} reverse secondary>
						You'll see within minutes why our concise online textbook is a cut above other Praxis Reading exam prep
						courses - it's easy to understand and written in plain English, filled with straightforward explanations
						that cut right to the chase. Achievable Praxis Reading study materials are easy to read, mobile-friendly,
						and include detailed walkthroughs of sample questions.
					</OnlineTextbook>

					<ModernPlatform colorIndex={4} product={PRAXIS_READING_SEO.sku} reverseImage secondary>
						Whether studying on the web or on a smartphone, the Achievable Praxis Reading prep platform UX is clean and
						responsive. Progress charts highlight your journey through the exam course content and your current strength
						in each section.
					</ModernPlatform>

					<AdaptiveStudyPlanner product={PRAXIS_READING_SEO.sku} reverse secondary>
						Use our study planner tool to create a personalized day-by-day study plan and ensure you complete all your
						Praxis Reading content by exam day. All you need to do is focus on studying, and our system will help you
						stay on track.
					</AdaptiveStudyPlanner>
				</FeatureSections>

				<ProvenSuccess product={PRAXIS_READING_SEO.sku} />

				<PassGuaranteedCta
					exam={PRAXIS_READING_SEO.shortExamName}
					href={PRAXIS_READING_SEO.firstPage}
					noGuarantee={!PRAXIS_READING_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
