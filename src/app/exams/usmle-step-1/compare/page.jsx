import Hero from '@components/exams/compare/Hero';
import Table from '@components/exams/compare/Table';
import Layout from '@components/layout';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import USMLE_COMPARE from '@constants/compare/usmleStep1';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const USMLE_SEO = SEO_PRODUCT['usmle-step-1'];
const { sku } = USMLE_SEO;
const canonical = CANONICAL_URLS.examsUsmleStep1Compare;

export const metadata = generatePageMetadata({
	description:
		"What's the best USMLE Step 1 prep program? Our comprehensive comparison of Achievable, <PERSON>, Smash USMLE, Am<PERSON><PERSON>, and Doctors in Training shows you which USMLE Step 1 exam prep program is best for you.",
	keywords: '<PERSON>, <PERSON><PERSON> USMLE, <PERSON><PERSON><PERSON>, Doctors in Training, USMLE, USMLE Step 1, USMLE Exam Prep, best USMLE prep',
	title: "Compare Achievable's USMLE prep to Kaplan, Smash USMLE, Amboss, and more",
	canonical,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<Hero description='Compare the best USMLE Step 1 exam prep courses. See why Achievable is the top choice for medical students and aspiring doctors.' />
		<Table href={USMLE_SEO.firstPage} rows={USMLE_COMPARE} />
		<PassGuaranteedCta
			exam={USMLE_SEO.fullExamName}
			href={USMLE_SEO.firstPage}
			detailText={USMLE_SEO.description}
			type='usmle'
			secondary
		/>
	</Layout>
);

export default Page;
