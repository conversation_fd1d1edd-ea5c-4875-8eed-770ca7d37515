import numeral from 'numeral';

import TrackProduct from '@components/TrackProduct';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import KeyStats from '@components/exams/usmle-step-1/overview/KeyStats';
import SocialProof from '@components/exams/usmle-step-1/overview/SocialProof';
import UsmleProcess from '@components/exams/usmle-step-1/overview/UsmleProcess';
import UsmleScore from '@components/exams/usmle-step-1/overview/UsmleScore';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';

import COURSE_STATS from '@constants/courseStats';
import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const USMLE_STATS = COURSE_STATS['usmle-step-1'];
const USMLE_SEO = SEO_PRODUCT['usmle-step-1'];
const { title, description, keywords, sku, imageUrl, canonical } = USMLE_SEO;
const numChapterQuizzes = numeral(USMLE_STATS.numChapterQuizzes).format('0,0');

// TODO add schema for reviews
const REVIEWS = [
	{
		rating: 5,
		attribution: 'Tori, University of Texas',
		quote: 'Professional and easy to use, with lots of good information not seen in many other similar products.',
	},
	{
		rating: 5,
		attribution: 'Shubham, Baylor',
		quote:
			'Good UI and sensible layout. Material is presented in a logical and easy to follow manner. recommend using the suggested study method highlighted in the intro.',
	},
	{
		rating: 5,
		attribution: 'Steven, Student',
		quote:
			'Very simple to grasp concepts with well laid out formatting. Definitely makes getting through topics easy and efficient.',
	},
];

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />

		<OverviewHero
			product={USMLE_SEO.sku}
			title={
				<>
					Pass the <ThemeText noWrap>USMLE Step 1</ThemeText>
				</>
			}
			description={
				<div className={styles.description}>
					<div>
						The United States Medical Licensing (USMLE) Step 1 exam is the first medical licensing exam you need to pass
						to become a MD. Achievable is the only USMLE exam prep course that uses memory science technology to ensure
						you pass.
					</div>
					<div>
						Achievable exam prep includes our complete online textbook and {numChapterQuizzes}+ topic review questions.
					</div>
				</div>
			}
			tag='USMLE STEP 1 EXAM PREP'
		/>

		<Reviews noun='students' reviews={REVIEWS} />

		<SocialProof />

		<IncludesEverything
			course={USMLE_SEO.shortExamName}
			href={USMLE_SEO.offerUrl}
			price={USMLE_SEO.priceF}
			product={USMLE_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization alt='Screenshot of Achievable USMLE Step 1 memory progress' product={USMLE_SEO.sku} />

			<ReviewQuizzes product={USMLE_SEO.sku} reverse secondary />

			<OnlineTextbook colorIndex={2} product={USMLE_SEO.sku} reverseImage secondary>
				You&apos;ll see within minutes why our concise online textbook is a cut above other USMLE Step 1 exam prep -
				it&apos;s easy to understand and written in plain English, filled with straightforward explanations cutting
				right to the chase. Achievable USMLE Step 1 study materials are easy to read, mobile friendly, and include
				detailed walkthroughs of sample questions. Each of the USMLE Step 1 sections are covered in detail in our USMLE
				Step 1 online course.
			</OnlineTextbook>

			<ModernPlatform product={USMLE_SEO.sku} reverse secondary />

			<AdaptiveStudyPlanner product={USMLE_SEO.sku} secondary />
		</FeatureSections>

		<TryAchievableCta product={USMLE_SEO.sku} />

		<ProvenSuccess product='usmle' reverse secondary />

		<KeyStats />

		<UsmleProcess />

		<TryAchievableCta product={USMLE_SEO.sku} />

		<UsmleScore />

		<ExamOverview seoProduct={USMLE_SEO} />

		<AuthorSpotlight author='sujata' reverseBackground />

		<PassGuaranteedCta exam={USMLE_SEO.shortExamName} href={USMLE_SEO.firstPage} type='usmle' />
	</Layout>
);

export default Page;
