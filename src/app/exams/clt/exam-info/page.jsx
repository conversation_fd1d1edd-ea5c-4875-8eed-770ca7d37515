import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/clt/Sidebar';
import Layout from '@components/layout';
import ExamOverview from '@components/libraryV2/ExamOverview';
import HeroStretch from '@components/libraryV2/HeroStretch';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SEO = SEO_PRODUCT.clt;
const CLT_SEO = { ...SEO, canonical: CANONICAL_URLS.examsCltExamInfo };

const { title, description, keywords, sku, canonical } = CLT_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Content = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>CLT</ThemeText> Exam information
					</>
				}
				titleTag='h1'
				description="What it is, what's tested, and how it's scored."
			/>

			<SidebarDesktop>
				<ExamOverview seoProduct={CLT_SEO} outlineSecondary={false} summarySecondary={true} hideCta />

				<PassGuaranteedCta
					exam={CLT_SEO.fullExamName}
					href={CLT_SEO.firstPage}
					tagline={
						<>
							Hit your <ThemeText>target score</ThemeText>
						</>
					}
					detailText="Achievable is the best online CLT exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
					type='clt'
					noGuarantee
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Content;
