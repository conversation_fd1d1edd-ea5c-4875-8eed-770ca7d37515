import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/clt/Sidebar';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const CLT_SEO = SEO_PRODUCT.clt;

const canonical = CANONICAL_URLS.examsCltFreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description: 'Take our free CLT practice exam and see if you can pass answering these CLT practice questions.',
	keywords:
		'clt practice exam, clt exam questions, clt sample questions, clt practice questions, clt questions, clt example questions, clt practice exam questions, clt practice test, clt test questions, clt practice test questions, classic learning test practice',
	title: `Free CLT Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the CLT Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku: 'clt',
	canonical,
});

const Clt = () => {
	return (
		<Layout jsonLd={{ schemas }}>
			<TrackProduct sku={CLT_SEO.sku} />

			<SidebarMobile />

			<PracticeExamHero seoProduct={CLT_SEO} />

			<SidebarDesktop>
				<PracticeQuiz seoProduct={CLT_SEO} examOnly />
			</SidebarDesktop>
		</Layout>
	);
};

export default Clt;
