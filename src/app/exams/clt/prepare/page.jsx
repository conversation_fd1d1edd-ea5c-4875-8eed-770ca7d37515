import TrackProduct from '@components/TrackProduct';
import EndlessMathPractice from '@components/exams/clt/EndlessMathPractice';
import GrammarAndWriting from '@components/exams/clt/GrammarAndWriting';
import { SidebarDesktop, SidebarMobile } from '@components/exams/clt/Sidebar';
import SmartVerbalReasoning from '@components/exams/clt/SmartVerbalReasoning';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import Typer from '@components/libraryV2/Typer';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const CLT_SEO = SEO_PRODUCT.clt;

const TYPER_ENTRIES = [
	'Liberty University',
	'University of Florida',
	"St. John's College",
	'Florida State University',
	'Wheaton College',
	'Covenant College',
	'Florida Tech',
	'Samford University',
	'University of South Florida',
	"St. Mary's College",
	'University of Dallas',
	'University of Saint Thomas',
	'Harding University',
];

const { title, description, keywords, sku } = CLT_SEO;
const canonical = CANONICAL_URLS.examsCltPrepare;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Content = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<OverviewHero
				product={CLT_SEO.sku}
				title={
					<>
						<div>The first step to admission at</div>
						<div>
							<ThemeText>
								<Typer entries={TYPER_ENTRIES} stinger='Your Top Choice' />
							</ThemeText>
						</div>
					</>
				}
				description="You know what score you need to apply to your dream school. Achievable is the only CLT prep course created in partnership with the CLT's official exclusive test preparation provider: Clayborne Education. Achievable is also the only CLT prep course that uses memory science-enhanced practice to ensure you reach your goal."
				tag='CLT EXAM PREP'
			/>

			<SidebarDesktop>
				<Reviews />

				<IncludesEverything
					course={CLT_SEO.shortExamName}
					href={CLT_SEO.offerUrl}
					price={CLT_SEO.priceF}
					product={CLT_SEO.sku}
				/>

				<FeatureSections>
					<EndlessMathPractice />

					<GrammarAndWriting />

					<SmartVerbalReasoning colorIndex={2} reverse reverseImage />

					<ModernPlatform colorIndex={3} product='clt' secondary />

					<OnlineTextbook product='clt' colorIndex={4} reverse reverseImage secondary>
						Learn how to answer hard CLT questions in less time using proven strategies and pro tips from our expert
						author team with 30+ years of experience. Achievable&apos;s CLT course is easy to understand, mobile
						friendly, and includes detailed walkthroughs of practice questions. Achievable is the easiest way to learn
						the CLT.
					</OnlineTextbook>

					<AdvancedPersonalization product='clt' />

					<AdaptiveStudyPlanner product='clt' reverse secondary />
				</FeatureSections>

				<ProvenSuccess product='clt' />

				<PassGuaranteedCta
					exam={CLT_SEO.fullExamName}
					href={CLT_SEO.firstPage}
					tagline={
						<>
							Hit your <ThemeText>target score</ThemeText>
						</>
					}
					detailText="Achievable is the best online CLT exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
					type='clt'
					noGuarantee
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Content;
