import GreScoreGuarantee from '@components/exams/gre/score-guarantee/ScoreGuarantee';
import Layout from '@components/layout';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const GRE_SEO = SEO_PRODUCT.gre;
const { sku } = GRE_SEO;
const canonical = CANONICAL_URLS.examsGreScoreGuarantee;

export const metadata = generatePageMetadata({
	canonical,
	description:
		"Achievable's GRE course is guaranteed effective: if we don't improve your score by at least 7 points, we'll give you a full refund.",
	keywords:
		'Gre, gre test, gre scores, gre test dates, gre practice test, gre scores, gre percentiles, gre exam, gre prep, gre exam prep, gre vocabulary, gre practice questions, gre score',
	title: "Achievable GRE's score guarantee and refund policy",
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<GreScoreGuarantee />
		<PassGuaranteedCta
			exam='GRE'
			href={GRE_SEO.firstPage}
			tagline={
				<>
					Hit your <ThemeText>target score</ThemeText>
				</>
			}
			type='gre'
			noGuarantee
		/>
	</Layout>
);

export default Page;
