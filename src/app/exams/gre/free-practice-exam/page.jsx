import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const greSeo = SEO_PRODUCT.gre;
const { sku } = greSeo;
const canonical = CANONICAL_URLS.examsGreFreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Take our free GRE Quant practice exam and see if you can pass answering these GRE Quant practice questions.',
	keywords:
		'gre quant practice exam, gre quant exam questions, gre quant sample questions, gre quant practice questions, gre quant questions, gre quant example questions, gre quant practice exam questions, gre mock test, gre quant mock test, gre mock test questions',
	title: `Free GRE Quantitative Reasoning Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the GRE Quantitative Reasoning Exam`,
});

const seoProduct = {
	...greSeo,
	shortExamName: 'GRE Quant',
	fullExamName: 'GRE Quantitative Reasoning',
};

const detailsCopy = `Solve each of the ${seoProduct.shortExamName} practice test questions below to get a feel for what to expect on the actual ${seoProduct.shortExamName} exam. Achievable's free ${seoProduct.shortExamName} practice questions are scored instantly, providing the correct answer along with an easy to understand explanation. Our GRE mock test questions are hand-crafted to test your ${seoProduct.shortExamName} knowledge. Get started on the path to passing the ${seoProduct.shortExamName} exam by solving 10 ${seoProduct.shortExamName} exam sample questions.`;

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz detailsCopy={detailsCopy} seoProduct={seoProduct} />
	</Layout>
);

export default Page;
