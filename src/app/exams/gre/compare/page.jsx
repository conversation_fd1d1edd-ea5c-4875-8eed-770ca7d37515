import Hero from '@components/exams/compare/Hero';
import Table from '@components/exams/compare/Table';
import Layout from '@components/layout';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import GRE_COMPARE from '@constants/compare/gre';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const GRE_SEO = SEO_PRODUCT.gre;
const { sku } = GRE_SEO;
const canonical = CANONICAL_URLS.examsGreCompare;

export const metadata = generatePageMetadata({
	canonical,
	description:
		"What's the best GRE prep program? Our comprehensive comparison of Achievable, Kaplan, Princeton Review, Manhattan Prep, and Economist shows you which GRE exam prep program is best for you.",
	keywords:
		'Kaplan, Princeton Review, Manhattan Prep, Economist, GRE, Graduate Record Exam, GRE Exam Prep, best GRE prep',
	title: "Compare Achievable's GRE prep to Kaplan, Princeton Review, Manhattan Prep, and more",
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<Hero description='Compare the best GRE exam prep courses. See why Achievable is the top choice for students and young professionals.' />
		<Table rows={GRE_COMPARE} />
		<PassGuaranteedCta
			exam='GRE'
			href={GRE_SEO.firstPage}
			tagline={
				<>
					Hit your <ThemeText>target score</ThemeText>
				</>
			}
			detailText={GRE_SEO.description}
			type='gre'
			noGuarantee
			secondary
		/>
	</Layout>
);

export default Page;
