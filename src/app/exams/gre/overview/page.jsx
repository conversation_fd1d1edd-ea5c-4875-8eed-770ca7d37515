import TrackProduct from '@components/TrackProduct';
import EndlessQuantitativeQuizzes from '@components/exams/gre/overview/EndlessQuantitativeQuizzes';
import GreLength from '@components/exams/gre/overview/GreLength';
import GreOrGmat from '@components/exams/gre/overview/GreOrGmat';
import GreScore from '@components/exams/gre/overview/GreScore';
import InstantEssayGrading from '@components/exams/gre/overview/InstantEssayGrading';
import GreSpotlight from '@components/exams/gre/overview/Spotlight';
import VerbalReasoning from '@components/exams/gre/overview/VerbalReasoning';
import VocabularyBuilding from '@components/exams/gre/overview/VocabularyBuilding';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Typer from '@components/libraryV2/Typer';
import Resources from '@components/resources';
import FEATURED_REVIEWS from '@components/reviews/Quotes/greQuotesData';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const GRE_SEO = SEO_PRODUCT.gre;
const { title, description, keywords, sku, imageUrl, canonical } = GRE_SEO;

const TYPER_ENTRIES = [
	'Stanford GSB',
	'Columbia Business School',
	'Harvard Business School',
	'Chicago Booth',
	'Tuck School of Business',
	'Ross School of Business',
	"Cornell's Johnson",
	'Haas School of Business',
	'Yale School of Management',
	"Northwestern's Kellogg",
	'MIT Sloan',
	"Duke's Fuqua",
];

export const metadata = generatePageMetadata({
	canonical,
	title,
	description,
	keywords,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={GRE_SEO.sku}
			title={
				<div className={styles.title}>
					<div>The first step to admission at</div>
					<div>
						<ThemeText>
							<Typer entries={TYPER_ENTRIES} stinger='Your Top Choice' />
						</ThemeText>
					</div>
				</div>
			}
			description={
				<div className={styles.description}>
					<div>
						You know what score you need to apply to your dream school. Achievable is the only GRE exam prep course that
						uses memory science technology to ensure you reach your goal.
					</div>
					<div>
						Achievable GRE test prep includes our online textbook, review questions, and full-length practice exams.
					</div>
					<div className={styles.guarantee}>100% money back guarantee</div>
				</div>
			}
			tag='GRE EXAM PREP'
		/>

		<Reviews className={styles.reviews} noun='students' reviews={FEATURED_REVIEWS} />

		<IncludesEverything
			course={GRE_SEO.shortExamName}
			href={GRE_SEO.offerUrl}
			price={GRE_SEO.priceF}
			product={GRE_SEO.sku}
		/>

		<FeatureSections>
			<EndlessQuantitativeQuizzes />

			<AdvancedPersonalization product='gre' />

			<VerbalReasoning />

			<VocabularyBuilding colorIndex={2} reverseImage />

			<InstantEssayGrading colorIndex={3} />

			<OnlineTextbook product='gre' colorIndex={4} reverseImage secondary>
				Learn how to answer GRE questions in less time using proven strategies from our expert author with 10+ years of
				experience. Achievable&apos;s GRE test prep materials are easy to understand, mobile friendly, and include
				detailed walkthroughs of sample questions. Each of the GRE sections are covered in detail in our online GRE prep
				course.
			</OnlineTextbook>

			<VideosOnKeyTopics product='gre' reverse secondary />

			<ModernPlatform product='gre' secondary />
		</FeatureSections>

		<TryAchievableCta product='gre' />

		<ProvenSuccess product='gre' reverse secondary />

		<GreSpotlight />

		<GreOrGmat secondary />

		<GreScore />

		<GreLength reverse secondary />

		<TryAchievableCta product='gre' />

		<ExamOverview seoProduct={GRE_SEO} secondary />

		<Resources
			copy='Achievable has you covered for the GRE. Check out our GRE Snacks podcast and collection of official ETS resources hand-picked to help you prepare for the GRE exam.'
			seoProduct={GRE_SEO}
			secondary
		/>

		<AuthorSpotlight author='mattRoy' />

		<PassGuaranteedCta
			exam={GRE_SEO.name}
			href={GRE_SEO.firstPage}
			tagline={
				<>
					Hit your <ThemeText>target score</ThemeText>
				</>
			}
			detailText="Achievable is the best online GRE exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
			type='gre'
			noGuarantee
			secondary
		/>
	</Layout>
);

export default Page;
