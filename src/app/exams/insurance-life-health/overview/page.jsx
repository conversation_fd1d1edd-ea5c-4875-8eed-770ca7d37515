import TrackProduct from '@components/TrackProduct';
import KeyQuestions from '@components/exams/insurance-life-health/KeyQuestions';
import KeyStats from '@components/exams/insurance-life-health/KeyStats';
import SocialProof from '@components/exams/insurance/SocialProof';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';

import COURSE_STATS from '@constants/courseStats';
import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const LIFE_AND_HEALTH_SEO = SEO_PRODUCT['insurance-life-health'];
const LIFE_AND_HEALTH_STATS = COURSE_STATS['insurance-life-health'];

const { title, description, keywords, sku, imageUrl, canonical } = LIFE_AND_HEALTH_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={LIFE_AND_HEALTH_SEO.sku}
			title={
				<div className={styles.title}>
					Pass the <ThemeText>Life & Health insurance exam</ThemeText>
				</div>
			}
			description={
				<div className={styles.description}>
					<div>
						Whether you&apos;re looking to become a life and health insurance agent, or getting your life and health
						license as part of a wealth management role, Achievable is the best and most effective life & health
						insurance exam prep on the market.
					</div>
					<div>
						Achievable exam prep includes our online textbook, review questions, and full-length practice exams.
					</div>
				</div>
			}
			tag={
				<div>
					LIFE & HEALTH <span className={styles.hideMobile}>INSURANCE </span>EXAM PREP
				</div>
			}
		/>

		<Reviews />

		<SocialProof />

		<IncludesEverything
			course={LIFE_AND_HEALTH_SEO.shortExamName}
			href={LIFE_AND_HEALTH_SEO.offerUrl}
			price={LIFE_AND_HEALTH_SEO.priceF}
			product={LIFE_AND_HEALTH_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization
				alt='Screenshot of Achievable Life & Health memory progress'
				product={LIFE_AND_HEALTH_SEO.sku}
			/>

			<FullLengthPracticeExams product={LIFE_AND_HEALTH_SEO.sku} reverse secondary>
				With hundreds of high-quality hand-crafted {LIFE_AND_HEALTH_SEO.fullExamName} practice exam questions, you can
				take {LIFE_AND_HEALTH_STATS.numPracticeExams}+ full-length practice exams. Our{' '}
				{LIFE_AND_HEALTH_SEO.shortExamName} practice questions include all the types of questions you&apos;ll see on the
				exam, and they&apos;re weighted according to the official {LIFE_AND_HEALTH_SEO.fullExamName} exam rubric for
				each state so you can have confidence in your scores.
			</FullLengthPracticeExams>

			<ReviewQuizzes colorIndex={2} product={LIFE_AND_HEALTH_SEO.sku} reverseImage secondary />

			<OnlineTextbook colorIndex={3} product={LIFE_AND_HEALTH_SEO.sku} reverse secondary>
				You&apos;ll see within minutes why our concise online textbook is a cut above other Life & Health insurance exam
				prep - it&apos;s easy to understand and written in plain English, filled with straightforward explanations that
				cut right to the chase. Achievable Life & Health insurance exam study materials are easy to read,
				mobile-friendly, and include detailed walkthroughs of sample questions. Our Life & Health insurance exam online
				course covers each of the Life & Health insurance exam sections in detail.
			</OnlineTextbook>

			<ModernPlatform product={LIFE_AND_HEALTH_SEO.sku} reverseImage secondary />

			<AdaptiveStudyPlanner product={LIFE_AND_HEALTH_SEO.sku} reverse secondary />
		</FeatureSections>

		<TryAchievableCta product={LIFE_AND_HEALTH_SEO.sku} />

		<ProvenSuccess product='insurance' secondary />

		<KeyStats />

		<KeyQuestions />

		<TryAchievableCta product={LIFE_AND_HEALTH_SEO.sku} />

		<ExamOverview noWrap={false} seoProduct={LIFE_AND_HEALTH_SEO} secondary hideCta />

		<AuthorSpotlight author='mattWilliams' stacked />

		<PassGuaranteedCta
			detailText="Achievable is the best online exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time."
			tagline={<div>Pass the L&H insurance exam.</div>}
			href={LIFE_AND_HEALTH_SEO.firstPage}
			type='insurance'
			secondary
		/>
	</Layout>
);

export default Page;
