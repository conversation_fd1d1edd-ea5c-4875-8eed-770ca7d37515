import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-series-9'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSeries9FreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Take our free FINRA Series 9 practice exam and see if you can pass answering these Series 9 practice questions.',
	keywords:
		'series 9 practice exam, series 9 exam questions, series 9 sample questions, series 9 practice questions, series 9 questions, series 9 example questions, series 9 practice exam questions, finra series 9 practice exam',
	title: `Free Series 9 Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the Series 9 Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
