import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-series-63'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSeries63FreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Take our free FINRA Series 63 practice exam and see if you can pass answering these Series 63 practice questions.',
	keywords:
		'series 63 practice exam, series 63 exam questions, series 63 sample questions, series 63 practice questions, series 63 questions, series 63 example questions, series 63 practice exam questions, finra series 63 practice exam',
	title: `Free Series 63 Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the Series 63 Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
