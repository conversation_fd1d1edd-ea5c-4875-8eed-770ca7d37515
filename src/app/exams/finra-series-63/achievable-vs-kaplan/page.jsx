import TrackProduct from '@components/TrackProduct';
import Quotes from '@components/exams/compare/Quotes';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import S63_ACHIEVABLE_VS_KAPLAN from '@constants/compare/finraSeries63AchievableVsKaplan';
import { ACHIEVABLE_VS_KAPLAN_S63_QUOTES } from '@constants/quotes';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const S63_SEO = SEO_PRODUCT['finra-series-63'];
const { sku } = S63_SEO;
const canonical = CANONICAL_URLS.examsFinraSeries63AchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated Series 63 courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords:
		'Achievable, Kaplan, FINRA Series 63, Series 63, Series 63 Exam Prep, best Series 63 prep, test prep, best Series 63 test prep, S63',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan Series 63 alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Pass your ${S63_SEO.shortExamName} on the first try with Achievable's up-to-date materials and modern study platform.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable Series 63's easy-to-understand material and adaptive learning platform gets you to pass in less time. These comparison stats come directly from the Kaplan Series 63 exam site.";

const STATS_DATA = generateStatsCard({ savings: 80 });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable S63'
			competitor='Kaplan S63'
			description={VERSUS_DESCRIPTION}
			seoProduct={S63_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={S63_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={S63_SEO.firstPage}
			rows={S63_ACHIEVABLE_VS_KAPLAN}
			size='cat'
			theme='versus'
		/>

		<Quotes competitor='Kaplan' course={S63_SEO.shortExamName} quotes={ACHIEVABLE_VS_KAPLAN_S63_QUOTES} />

		<IncludesEverything
			course={S63_SEO.shortExamName}
			href={S63_SEO.offerUrl}
			price={S63_SEO.priceF}
			product={S63_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S63_SEO.sku} />

			<FullLengthPracticeExams product={S63_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S63_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S63_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S63_SEO.sku} secondary />

			<ModernPlatform colorIndex={4} product={S63_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<PassGuaranteedCta
			exam={S63_SEO.fullExamName}
			href={S63_SEO.firstPage}
			detailText={S63_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
