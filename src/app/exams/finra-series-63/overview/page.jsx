import TrackProduct from '@components/TrackProduct';
import SocialProof from '@components/exams/finra/SocialProof';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import AskAchievableAI from '@components/exams/shared/AskAchievableAI';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import HowToGet from '@components/exams/shared/HowToGet';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import NarratedAudio from '@components/exams/shared/NarratedAudio';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import PracticeExam from '@components/exams/shared/PracticeExam';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import WhatsCovered from '@components/exams/shared/WhatsCovered';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import Link from '@components/libraryV2/Link';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Resources from '@components/resources';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const S63_SEO = SEO_PRODUCT['finra-series-63'];
const { title, description, keywords, canonical, sku, imageUrl } = S63_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={S63_SEO.sku}
			title={
				<>
					Pass the <ThemeText noWrap>Series 63</ThemeText>
				</>
			}
			description={
				<div className={styles.description}>
					<div>
						You already have knowledge of the finance industry and your role. Now, knock out the Series 63 with
						Achievable - the best and most effective Series 63 exam prep on the market.
					</div>
					<div>
						Achievable exam prep includes our online textbook, review questions, and full-length practice exams.
					</div>
				</div>
			}
			tag='FINRA SERIES 63 EXAM PREP'
		/>

		<Reviews />

		<SocialProof />

		<IncludesEverything
			course={S63_SEO.shortExamName}
			href={S63_SEO.offerUrl}
			price={S63_SEO.priceF}
			product={S63_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S63_SEO.sku} />

			<FullLengthPracticeExams product={S63_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S63_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S63_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S63_SEO.sku} secondary />

			<NarratedAudio colorIndex={4} product={S63_SEO.sku} reverse secondary />

			<AskAchievableAI colorIndex={5} product={S63_SEO.sku} secondary />

			<ModernPlatform colorIndex={6} product={S63_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<TryAchievableCta product={S63_SEO.sku} />

		<ProvenSuccess product='finra' secondary />

		<HowToGet product={S63_SEO.sku} reverse reverseBackground withBackground>
			<div>
				Financial professionals typically take the Series 63 as their final exam after completing their{' '}
				<Link theme='primary' to='/exams/finra-sie/overview/'>
					SIE Exam
				</Link>{' '}
				and{' '}
				<Link theme='primary' to='/exams/finra-series-6/overview/'>
					Series 6
				</Link>
				,{' '}
				<Link theme='primary' to='/exams/finra-series-7/prepare/'>
					7
				</Link>
				, or 79 license. However, you can take the Series 63 as your first FINRA exam. Unlike most FINRA exams, you do
				not need a sponsor to take the Series 63.
			</div>
		</HowToGet>

		<WhatsCovered product={S63_SEO.sku} secondary>
			The Series 63 is known as the &quot;state law test&quot;, and covers two main areas: State Securities Acts and the
			related rules and regulations, and ethical practices and fiduciary obligations. You&apos;ll learn about the
			Uniform Securities Act (the &quot;USA&quot;) and how this framework of laws and regulations was created for and
			adopted by the fifty states in the United States. And you&apos;ll learn about how the USA-related laws are
			enforced across the country.
		</WhatsCovered>

		<PracticeExam product={S63_SEO.sku} reverse />

		<ExamOverview cta={<TryAchievableCta product={S63_SEO.sku} secondary />} seoProduct={S63_SEO} secondary />

		<Resources
			copy='Check out our free Series 63 cheat sheets / Series 63 dump sheets and podcasts to help you prepare for the FINRA Series 63 exam or for a quick refresher on the Series 63 fundamentals.'
			seoProduct={S63_SEO}
			secondary
		/>

		<AuthorSpotlight author='brandonRith' />

		<PassGuaranteedCta
			exam={S63_SEO.fullExamName}
			href={S63_SEO.firstPage}
			detailText="Achievable is the best Series 63 exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
