import TrackProduct from '@components/TrackProduct';
import FullLengthPracticeExams from '@components/exams/npte-pta/FullLengthPracticeExams';
import ReviewQuizzes from '@components/exams/npte-pta/ReviewQuizzes';
import { SidebarDesktop, SidebarMobile } from '@components/exams/npte-pta/Sidebar';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const NPTE_SEO = SEO_PRODUCT['npte-pta'];
const { title, description, keywords, canonical = CANONICAL_URLS.examsNptePtaPrepare, imageUrl, sku } = NPTE_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<OverviewHero
				product={NPTE_SEO.sku}
				title={
					<>
						Pass the <ThemeText noWrap>NPTE exam</ThemeText>
					</>
				}
				description={
					<div className={styles.description}>
						<div>
							The NPTE (National Physical Therapy Exam) is the gateway to becoming a licensed physical therapist and
							physical therapist assistant. Achievable is the best and most effective NPTE exam prep on the market and
							the only prep course that uses memory science technology to ensure you pass on the first try.
						</div>
						<div>
							Achievable exam prep includes our complete online textbook, review questions, and full practice exams, all
							of which are easily accessible on your phone, tablet, or computer.
						</div>
					</div>
				}
				tag='NPTE EXAM PREP'
			/>

			<SidebarDesktop>
				<Reviews />

				<IncludesEverything
					course={NPTE_SEO.shortExamName}
					href={NPTE_SEO.offerUrl}
					price={NPTE_SEO.priceF}
					product={NPTE_SEO.sku}
				/>

				<FeatureSections>
					<AdvancedPersonalization product={NPTE_SEO.sku} secondary>
						Achievable NPTE prep uses adaptive learning techniques to create and update a personalized model of your
						memory, individually tracking your retention and mastery of each NPTE learning objective. Our learning
						engine monitors your study progress and continually adjusts your quiz questions to ensure you focus on the
						topics that matter most, improving study effectiveness and reducing overall study time.
					</AdvancedPersonalization>

					<FullLengthPracticeExams />

					<ReviewQuizzes />

					<OnlineTextbook product={NPTE_SEO.sku} colorIndex={3} reverse secondary>
						You'll see within minutes why our concise online textbook is a cut above other NPTE prep - it's easy to
						understand and written in plain English, filled with straightforward explanations that cut right to the
						chase. Achievable NPTE study materials are easy to read, mobile-friendly, and include detailed walkthroughs
						of sample questions. Our NPTE online course covers each of the exam sections in detail.
					</OnlineTextbook>

					<ModernPlatform colorIndex={4} product={NPTE_SEO.sku} reverseImage secondary>
						Whether studying on the web or on a smartphone, the Achievable NPTE prep platform UX is clean and
						responsive. Progress charts highlight your journey through the NPTE course content and your current strength
						in each section.
					</ModernPlatform>

					<AdaptiveStudyPlanner product={NPTE_SEO.sku} reverse secondary>
						Use our study planner tool to create a personalized day-by-day study plan and ensure you complete all your
						NPTE content by exam day. All you need to do is focus on studying, and our system will help you stay on
						track.
					</AdaptiveStudyPlanner>
				</FeatureSections>

				<ProvenSuccess product={NPTE_SEO.sku} />

				<PassGuaranteedCta
					exam={NPTE_SEO.shortExamName}
					href={NPTE_SEO.firstPage}
					noGuarantee={!NPTE_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
