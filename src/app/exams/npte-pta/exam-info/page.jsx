import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/npte-pta/Sidebar';
import Layout from '@components/layout';
import ExamOverview from '@components/libraryV2/ExamOverview';
import HeroStretch from '@components/libraryV2/HeroStretch';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SEO = SEO_PRODUCT['npte-pta'];
const NPTE_SEO = { ...SEO, canonical: CANONICAL_URLS.examsNptePtaExamInfo };
const { title, description, keywords, canonical, sku } = NPTE_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>NPTE</ThemeText> Exam information
					</>
				}
				titleTag='h1'
				description="What it is, what's tested, and how it's scored."
			/>

			<SidebarDesktop>
				<ExamOverview
					defaultExamName='PT/PTA'
					seoProduct={NPTE_SEO}
					outlineSecondary={false}
					summarySecondary={true}
					hideCta
				/>

				<PassGuaranteedCta
					exam={NPTE_SEO.shortExamName}
					href={NPTE_SEO.firstPage}
					noGuarantee={!NPTE_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
