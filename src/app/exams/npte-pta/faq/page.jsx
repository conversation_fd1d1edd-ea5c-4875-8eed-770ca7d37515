import classNames from 'classnames';

import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/npte-pta/Sidebar';
import Layout from '@components/layout';
import ContentIconRow from '@components/libraryV2/ContentIconRow';
import ContentWell from '@components/libraryV2/ContentWell';
import Heading from '@components/libraryV2/Heading';
import HeroStretch from '@components/libraryV2/HeroStretch';
import Link from '@components/libraryV2/Link';
import List from '@components/libraryV2/List';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const SEO = SEO_PRODUCT['npte-pta'];
const NPTE_SEO = { ...SEO, canonical: CANONICAL_URLS.examsNptePtaFaq };
const { title, description, keywords, canonical, sku } = NPTE_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>NPTE</ThemeText> FAQs
					</>
				}
				titleTag='h1'
				description='Answers to the most common questions about the National Physical Therapy Exam.'
			/>

			<SidebarDesktop>
				<ContentWell tighten>
					<Heading size='h2' tagType='h2'>
						What's the difference between <ThemeText>NPTE-PT</ThemeText> and <ThemeText>NPTE-PTA</ThemeText>?
					</Heading>
					<div className={classNames(styles.paragraph, styles.differ)}>
						<div>
							The NPTE-PT is the National Physical Therapist Exam for Physical Therapists, while the NPTE-PTA is the
							National Physical Therapist Exam for Physical Therapist Assistants. These are two different exams,
							tailored to different roles with different licenses.
						</div>
						<div>
							Physical therapists are responsible for diagnosing conditions and creating treatment plans for patients.
							Physical therapist assistants cannot create care plans for patients, however, they support patients
							through monitoring exercises and movements, providing massages, and conducting ice and heat treatments
							supervised by the Physical Therapist.
						</div>
						<div>
							Though both exams cover roughly the same topics, the main difference between the Physical Therapist (PT)
							exam and the Physical Therapist Assistant (PTA) exam is the length of the test. The PT exam is an hour
							longer, with an additional 45 questions. It also delves deeper into assessments and diagnoses, while the
							PTA exam covers more of the applications and treatments of patients.
						</div>
					</div>
				</ContentWell>

				<ContentIconRow
					iconClass='fa-regular fa-messages-question'
					title={
						<>
							What is a passing score on the <ThemeText>NPTE</ThemeText>? What is the pass rate?
						</>
					}
					reverse
					secondary
				>
					<div className={styles.paragraph}>
						<div>
							The NPTE is a pass/fail exam, scored from 200 to 800. Achieving a score of 600 or above is considered a
							passing score. Anything below 600 is considered failing.
						</div>
						<div>
							The average pass rate is around 82%, while those who have graduated from PT programs have an average pass
							rate of 86% on the first try.
						</div>
					</div>
				</ContentIconRow>

				<ContentIconRow
					iconClass='fa-duotone fa-books'
					title={
						<>
							Does passing the <ThemeText>NPTE</ThemeText> automatically qualify you for a license?
						</>
					}
					reverseBackground
					withBackground
				>
					No, passing the exam does not instantly lead to a license. There are several steps to obtaining a PT or PTA
					license, including graduating from an accredited program, applying for licensure, and, in some cases, taking a
					state or regional exam in addition to the NPTE. You can find more information on our blog post here.
				</ContentIconRow>

				<ContentIconRow
					className={styles.paragraph}
					iconClass='fa-sharp-duotone fa-brain-circuit'
					title={
						<>
							When can you take the <ThemeText>NPTE</ThemeText>?
						</>
					}
					reverse
					secondary
				>
					<div>The NPTE is administered only four times a year: in January, April, July, and October.</div>
					<div>
						It's important to understand the registration process so you can test in your preferred month; otherwise,
						you may have to wait until the next test date. You can find more information on test dates{' '}
						<Link
							href='https://www.fsbpt.org/Free-Resources/NPTE-Candidate-Handbook/Understanding-the-NPTE'
							rel='noopener noreferrer'
							target='_blank'
							theme='primary'
						>
							here
						</Link>
						.
					</div>
				</ContentIconRow>

				<ContentIconRow
					iconClass='fa-duotone fa-building-columns'
					title={
						<>
							What are the requirements to take the <ThemeText>NPTE</ThemeText>?
						</>
					}
				>
					<div>
						The requirements for taking the NPTE include the following:
						<div>
							<List className={styles.list}>
								<li>Must be 18+ years of age</li>
								<li>
									Must have a degree from a CAPTE (Commission on Accreditation in Physical Therapy Education) accredited
									PT program
								</li>
								<li>Must be approved by the licensing authority in the jurisdiction you intend to practice</li>
							</List>
						</div>
						You can find more information on eligibility requirements{' '}
						<Link
							href='https://www.fsbpt.org/Free-Resources/NPTE-Candidate-Handbook/Eligibility-Requirements'
							rel='noopener noreferrer'
							target='_blank'
							theme='primary'
						>
							here
						</Link>
						.
					</div>
				</ContentIconRow>

				<PassGuaranteedCta
					exam={NPTE_SEO.shortExamName}
					href={NPTE_SEO.firstPage}
					noGuarantee={!NPTE_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
