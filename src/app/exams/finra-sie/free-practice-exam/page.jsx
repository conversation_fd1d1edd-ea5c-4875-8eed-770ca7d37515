import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-sie'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSieFreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description: 'Take our free FINRA SIE practice exam and see if you can pass answering these SIE practice questions.',
	keywords:
		'sie practice exam, sie exam questions, sie sample questions, sie practice questions, sie questions, sie example questions, sie practice exam questions, finra sie practice exam',
	title: `Free SIE Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the SIE Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
