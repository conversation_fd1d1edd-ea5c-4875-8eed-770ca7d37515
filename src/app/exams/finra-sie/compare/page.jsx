import Hero from '@components/exams/compare/Hero';
import Table from '@components/exams/compare/Table';
import Layout from '@components/layout';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import SIE_COMPARE from '@constants/compare/finraSie';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SIE_SEO = SEO_PRODUCT['finra-sie'];
const { sku } = SIE_SEO;
const canonical = CANONICAL_URLS.examsFinraSieCompare;

export const metadata = generatePageMetadata({
	canonical,
	description:
		"What's the best SIE prep program? Our comprehensive comparison of Achievable, Kaplan, STC, PassPerfect, and <PERSON><PERSON><PERSON><PERSON> shows you which FINRA SIE exam prep program is best for you.",
	keywords: '<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FINRA SIE, <PERSON>IE, SIE Exam Prep, best SIE prep',
	title: "Compare Achievable's SIE prep to Kaplan, STC, PassPerfect, and more",
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<Hero description='Compare the best FINRA SIE Exam prep courses. See why Achievable is the top choice for college students and career switchers.' />
		<Table href={SIE_SEO.firstPage} rows={SIE_COMPARE} />
		<PassGuaranteedCta
			exam={SIE_SEO.fullExamName}
			href={SIE_SEO.firstPage}
			detailText={SIE_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
