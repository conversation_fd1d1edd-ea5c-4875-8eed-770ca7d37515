import TrackProduct from '@components/TrackProduct';
import Quotes from '@components/exams/compare/Quotes';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import SIE_ACHIEVABLE_VS_KAPLAN from '@constants/compare/finraSieAchievableVsKaplan';
import { ACHIEVABLE_VS_KAPLAN_SIE_QUOTES } from '@constants/quotes';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SIE_SEO = SEO_PRODUCT['finra-sie'];
const { sku } = SIE_SEO;
const canonical = CANONICAL_URLS.examsFinraSieAchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated SIE courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords: 'Achievable, Kaplan, FINRA SIE, SIE, SIE Exam Prep, best SIE prep, test prep, best SIE test prep',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan SIE alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Pass your SIE on the first try with Achievable's up-to-date materials and modern study platform.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable SIE's easy-to-understand material and adaptive learning platform gets you to pass in less time. These comparison stats come directly from the Kaplan SIE exam site.";

const STATS_DATA = generateStatsCard({ savings: 100 });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable SIE'
			competitor='Kaplan SIE'
			description={VERSUS_DESCRIPTION}
			seoProduct={SIE_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={SIE_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={SIE_SEO.firstPage}
			rows={SIE_ACHIEVABLE_VS_KAPLAN}
			size='cat'
			theme='versus'
		/>

		<Quotes competitor='Kaplan' course={SIE_SEO.shortExamName} quotes={ACHIEVABLE_VS_KAPLAN_SIE_QUOTES} />

		<IncludesEverything
			course={SIE_SEO.shortExamName}
			href={SIE_SEO.offerUrl}
			price={SIE_SEO.priceF}
			product={SIE_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={SIE_SEO.sku} />

			<FullLengthPracticeExams product={SIE_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={SIE_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={SIE_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={SIE_SEO.sku} secondary />

			<ModernPlatform colorIndex={4} product={SIE_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<PassGuaranteedCta
			exam={SIE_SEO.fullExamName}
			href={SIE_SEO.firstPage}
			detailText={SIE_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
