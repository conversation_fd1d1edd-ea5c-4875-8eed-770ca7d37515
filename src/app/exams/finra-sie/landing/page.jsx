import HeroWithVideo from '@components/exams/finra-sie/landing/HeroWithVideo';
import SocialProof from '@components/exams/finra/SocialProof';
import FinraSpotlight from '@components/exams/finra/Spotlight';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SIE_SEO = SEO_PRODUCT['finra-sie'];
const { title, description, keywords, imageUrl, sku, canonical } = SIE_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const FinraSie = () => (
	<Layout jsonLd={{ schemas }}>
		<HeroWithVideo />

		<Reviews />

		<SocialProof />

		<FinraSpotlight />

		<AuthorSpotlight author='brandonRith' secondary />

		<PassGuaranteedCta
			exam={SIE_SEO.fullExamName}
			href={SIE_SEO.firstPage}
			detailText="Achievable is the best SIE exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
			type='finra'
			secondary
		/>
	</Layout>
);

export default FinraSie;
