import TrackProduct from '@components/TrackProduct';
import HowHardIsIt from '@components/exams/finra-sie/overview/HowHardIsIt';
import WhatRolesRequireIt from '@components/exams/finra-sie/overview/WhatRolesRequireIt';
import WhoShouldTakeIt from '@components/exams/finra-sie/overview/WhoShouldTakeIt';
import WhySie from '@components/exams/finra-sie/overview/WhySie';
import SocialProof from '@components/exams/finra/SocialProof';
import FinraSpotlight from '@components/exams/finra/Spotlight';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import AskAchievableAI from '@components/exams/shared/AskAchievableAI';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import NarratedAudio from '@components/exams/shared/NarratedAudio';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Typer from '@components/libraryV2/Typer';
import Resources from '@components/resources';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SIE_SEO = SEO_PRODUCT['finra-sie'];
const { title, description, keywords, imageUrl, sku, canonical } = SIE_SEO;

const TYPER_ENTRIES = [
	'A Wealth Manager',
	'An Investment Banker',
	'A Research Analyst',
	'An Equity Trader',
	'A Financial Consultant',
	'A Stockbroker',
	'An Insurance Analyst',
	'A Mutual Fund Analyst',
	'An Operations Analyst',
];

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const FinraSie = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={SIE_SEO.sku}
			title={
				<>
					<div>The first step to becoming</div>
					<div>
						<ThemeText>
							<Typer entries={TYPER_ENTRIES} stinger='A Finance Professional' />
						</ThemeText>
					</div>
				</>
			}
			description="Passing the Securities Industry Essentials (SIE) exam proves to prospective employers that you're capable of handling a job in finance. We'll help you pass the SIE and take that first step. Achievable exam prep includes our online textbook, review questions, and full-length practice exams."
			tag='FINRA SIE EXAM PREP'
		/>

		<Reviews />

		<SocialProof />

		<IncludesEverything
			course={SIE_SEO.shortExamName}
			href={SIE_SEO.offerUrl}
			price={SIE_SEO.priceF}
			product={SIE_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={SIE_SEO.sku} />

			<FullLengthPracticeExams product={SIE_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={SIE_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={SIE_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={SIE_SEO.sku} secondary />

			<NarratedAudio colorIndex={4} product={SIE_SEO.sku} reverse secondary />

			<AskAchievableAI colorIndex={5} product={SIE_SEO.sku} secondary />

			<ModernPlatform colorIndex={6} product={SIE_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<TryAchievableCta product={SIE_SEO.sku} />

		<ProvenSuccess product='finra' secondary />

		<WhySie />

		<WhatRolesRequireIt />

		<WhoShouldTakeIt />

		<HowHardIsIt />

		<ExamOverview seoProduct={SIE_SEO} reverse />

		<Resources
			copy='Check out our free SIE cheat sheets / SIE dump sheets and podcasts to help you prepare for the FINRA SIE exam or for a quick refresher on the SIE fundamentals.'
			seoProduct={SIE_SEO}
		/>

		<FinraSpotlight />

		<AuthorSpotlight author='brandonRith' secondary />

		<PassGuaranteedCta
			exam={SIE_SEO.fullExamName}
			href={SIE_SEO.firstPage}
			detailText="Achievable is the best SIE exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
			type='finra'
			secondary
		/>
	</Layout>
);

export default FinraSie;
