import TrackProduct from '@components/TrackProduct';
import Quotes from '@components/exams/compare/Quotes';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import S66_ACHIEVABLE_VS_KAPLAN from '@constants/compare/finraSeries66AchievableVsKaplan';
import { ACHIEVABLE_VS_KAPLAN_S66_QUOTES } from '@constants/quotes';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const S66_SEO = SEO_PRODUCT['finra-series-66'];
const { sku } = S66_SEO;
const canonical = CANONICAL_URLS.examsFinraSeries66AchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated Series 66 courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords:
		'Achievable, Kaplan, FINRA Series 66, Series 66, Series 66 Exam Prep, best Series 66 prep, test prep, best Series 66 test prep, S66',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan Series 66 alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Pass your ${S66_SEO.shortExamName} on the first try with Achievable's up-to-date materials and modern study platform.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable Series 66's easy-to-understand material and adaptive learning platform gets you to pass in less time. These comparison stats come directly from the Kaplan Series 66 exam site.";

const STATS_DATA = generateStatsCard({ savings: 100 });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Compare = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable S66'
			competitor='Kaplan S66'
			description={VERSUS_DESCRIPTION}
			seoProduct={S66_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={S66_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={S66_SEO.firstPage}
			rows={S66_ACHIEVABLE_VS_KAPLAN}
			size='cat'
			theme='versus'
		/>

		<Quotes competitor='Kaplan' course={S66_SEO.shortExamName} quotes={ACHIEVABLE_VS_KAPLAN_S66_QUOTES} />

		<IncludesEverything
			course={S66_SEO.shortExamName}
			href={S66_SEO.offerUrl}
			price={S66_SEO.priceF}
			product={S66_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S66_SEO.sku} />

			<FullLengthPracticeExams product={S66_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S66_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S66_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S66_SEO.sku} secondary />

			<ModernPlatform colorIndex={4} product={S66_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<PassGuaranteedCta
			exam={S66_SEO.fullExamName}
			href={S66_SEO.firstPage}
			detailText={S66_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Compare;
