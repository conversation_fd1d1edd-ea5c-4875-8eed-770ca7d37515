import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-series-66'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSeries66FreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Take our free FINRA Series 66 practice exam and see if you can pass answering these Series 66 practice questions.',
	keywords:
		'series 66 practice exam, series 66 exam questions, series 66 sample questions, series 66 practice questions, series 66 questions, series 66 example questions, series 66 practice exam questions, finra series 66 practice exam',
	title: `Free Series 66 Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the Series 66 Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
