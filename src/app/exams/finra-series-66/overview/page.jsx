import TrackProduct from '@components/TrackProduct';
import SocialProof from '@components/exams/finra/SocialProof';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import AskAchievableAI from '@components/exams/shared/AskAchievableAI';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import HowToGet from '@components/exams/shared/HowToGet';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import NarratedAudio from '@components/exams/shared/NarratedAudio';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import PracticeExam from '@components/exams/shared/PracticeExam';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import WhatsCovered from '@components/exams/shared/WhatsCovered';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import Link from '@components/libraryV2/Link';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Resources from '@components/resources';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const S66_SEO = SEO_PRODUCT['finra-series-66'];
const { title, description, keywords, sku, imageUrl, canonical } = S66_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={S66_SEO.sku}
			title={
				<>
					Pass the <ThemeText noWrap>Series 66</ThemeText>
				</>
			}
			description={
				<div className={styles.description}>
					<div>
						You already have knowledge of the finance industry and your role. Now, knock out the Series 66 with
						Achievable - the best and most effective Series 66 exam prep on the market.
					</div>
					<div>
						Achievable exam prep includes our online textbook, review questions, and full-length practice exams.
					</div>
				</div>
			}
			tag='FINRA SERIES 66 EXAM PREP'
		/>

		<Reviews />

		<SocialProof />

		<IncludesEverything
			course={S66_SEO.shortExamName}
			href={S66_SEO.offerUrl}
			price={S66_SEO.priceF}
			product={S66_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S66_SEO.sku} />

			<FullLengthPracticeExams product={S66_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S66_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S66_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S66_SEO.sku} secondary />

			<NarratedAudio colorIndex={4} product={S66_SEO.sku} reverse secondary />

			<AskAchievableAI colorIndex={5} product={S66_SEO.sku} secondary />

			<ModernPlatform colorIndex={6} product={S66_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<TryAchievableCta product={S66_SEO.sku} />

		<ProvenSuccess product='finra' secondary />

		<HowToGet product={S66_SEO.sku} reverse reverseBackground withBackground>
			<div>
				The Series 66 does not have any prerequisites, and you do not need to be employed by or sponsored by a FINRA
				member firm in order to register for and take the exam. However, the{' '}
				<Link theme='primary' to='/exams/finra-sie/overview/'>
					SIE exam
				</Link>{' '}
				and <Link to='/exams/finra-series-7/prepare/'>Series 7</Link> are both corequisites, and all three must be
				completed to obtain the registration.
			</div>
		</HowToGet>

		<WhatsCovered product={S66_SEO.sku} secondary>
			<div>
				The Series 66 exam, known formally as the Uniform Investment Adviser Law Exam, covers the laws, regulations,
				ethics, strategies, and various topics important to the role of a financial adviser and offering financial
				advice, as well as the purchase and sale of securities and state laws pertaining to securities. This exam
				fulfills the requirements of both the Series 63 and Series 65, enabling the license holder to give financial
				advice and facilitate the purchase and sale of securities.
			</div>
			<div>
				Despite commonly being called the &quot;FINRA Series 66&quot; it is more accurately a North American Securities
				Administrators Association (NASAA) exam administered by FINRA.
			</div>
		</WhatsCovered>

		<PracticeExam product={S66_SEO.sku} reverse />

		<ExamOverview cta={<TryAchievableCta product={S66_SEO.sku} secondary />} seoProduct={S66_SEO} secondary />

		<Resources
			copy='Check out our free Series 66 cheat sheets / Series 66 dump sheets and podcasts to help you prepare for the FINRA Series 66 exam or for a quick refresher on the Series 66 fundamentals.'
			seoProduct={S66_SEO}
			secondary
		/>

		<AuthorSpotlight author='brandonRith' />

		<PassGuaranteedCta
			exam={S66_SEO.fullExamName}
			href={S66_SEO.firstPage}
			detailText="Achievable is the best Series 66 exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
