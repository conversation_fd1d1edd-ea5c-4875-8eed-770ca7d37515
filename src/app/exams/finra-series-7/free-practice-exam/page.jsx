import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-series-7'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSeries7FreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Take our free FINRA Series 7 practice exam and see if you can pass answering these Series 7 practice questions.',
	keywords:
		'series 7 practice exam, series 7 exam questions, series 7 sample questions, series 7 practice questions, series 7 questions, series 7 example questions, series 7 practice exam questions, finra series 7 practice exam',
	title: `Free Series 7 Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the Series 7 Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
