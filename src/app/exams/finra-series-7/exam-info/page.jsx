import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/finra-series-7/Sidebar';
import Layout from '@components/layout';
import ExamOverview from '@components/libraryV2/ExamOverview';
import HeroStretch from '@components/libraryV2/HeroStretch';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SEO = SEO_PRODUCT['finra-series-7'];
const S7_SEO = { ...SEO, canonical: CANONICAL_URLS.examsFinraSeries7ExamInfo };

const { title, description, keywords, canonical, sku, imageUrl } = S7_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Content = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<div>
						<ThemeText>FINRA Series 7</ThemeText>
						<div>Exam information</div>
					</div>
				}
				titleTag='h1'
				description="What it is, what's tested, and how it's scored."
			/>

			<SidebarDesktop>
				<ExamOverview seoProduct={S7_SEO} outlineSecondary={false} summarySecondary={true} hideCta />

				<PassGuaranteedCta
					exam={S7_SEO.fullExamName}
					href={S7_SEO.firstPage}
					detailText="Achievable is the best Series 7 exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
					type='finra'
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Content;
