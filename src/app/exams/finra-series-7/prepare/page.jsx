import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/finra-series-7/Sidebar';
import SocialProof from '@components/exams/finra/SocialProof';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import AskAchievableAI from '@components/exams/shared/AskAchievableAI';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import NarratedAudio from '@components/exams/shared/NarratedAudio';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import * as styles from './styles.module.scss';

const S7_SEO = SEO_PRODUCT['finra-series-7'];

const { title, description, keywords, canonical, sku, imageUrl } = S7_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Content = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<OverviewHero
				product={S7_SEO.sku}
				title={
					<>
						Pass the <ThemeText noWrap>Series 7</ThemeText>
					</>
				}
				description={
					<div className={styles.description}>
						<div>
							Whether you&apos;re aiming to become a financial advisor or renewing a lapsed license, Achievable is the
							best and most effective Series 7 exam prep on the market.
						</div>
						<div>
							Achievable exam prep includes our online textbook, review questions, and full-length practice exams.
						</div>
					</div>
				}
				tag='FINRA SERIES 7 EXAM PREP'
			/>

			<SidebarDesktop>
				<Reviews />

				<SocialProof />

				<IncludesEverything
					course={S7_SEO.shortExamName}
					href={S7_SEO.offerUrl}
					price={S7_SEO.priceF}
					product={S7_SEO.sku}
				/>

				<FeatureSections>
					<AdvancedPersonalization product={S7_SEO.sku} />

					<FullLengthPracticeExams product={S7_SEO.sku} reverse secondary withDetails />

					<ReviewQuizzes colorIndex={2} product={S7_SEO.sku} reverseImage secondary />

					<OnlineTextbook product={S7_SEO.sku} colorIndex={3} reverse secondary />

					<VideosOnKeyTopics product={S7_SEO.sku} secondary />

					<NarratedAudio colorIndex={4} product={S7_SEO.sku} reverse secondary />

					<AskAchievableAI colorIndex={5} product={S7_SEO.sku} secondary />

					<ModernPlatform colorIndex={6} product={S7_SEO.sku} reverse reverseImage secondary />
				</FeatureSections>

				<ProvenSuccess product='finra' />

				<PassGuaranteedCta
					exam={S7_SEO.fullExamName}
					href={S7_SEO.firstPage}
					detailText="Achievable is the best Series 7 exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
					type='finra'
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Content;
