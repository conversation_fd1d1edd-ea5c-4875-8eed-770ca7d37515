import TrackProduct from '@components/TrackProduct';
import Hero from '@components/exams/compare/Hero';
import Table from '@components/exams/compare/Table';
import Layout from '@components/layout';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import S7_COMPARE from '@constants/compare/finraSeries7';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const S7_SEO = SEO_PRODUCT['finra-series-7'];
const { sku } = S7_SEO;
const canonical = CANONICAL_URLS.examsFinraSeries7Compare;

export const metadata = generatePageMetadata({
	canonical,
	description:
		"What's the best Series 7 prep program? Our comprehensive comparison of <PERSON>chievable, <PERSON>, STC, PassPerfect, and <PERSON><PERSON><PERSON><PERSON> shows you which FINRA Series 7 exam prep program is best for you.",
	keywords: '<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FINRA Series 7, Series 7, Series 7 Exam Prep, best Series 7 prep',
	title: "Compare Achievable's Series 7 prep to Kaplan, STC, Passperfect, and more",
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<Hero description='Compare the best FINRA Series 7 exam prep courses. See why Achievable is the top choice for young professionals and industry veterans alike.' />
		<Table href={S7_SEO.firstPage} rows={S7_COMPARE} />
		<PassGuaranteedCta
			exam={S7_SEO.fullExamName}
			href={S7_SEO.firstPage}
			detailText={S7_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
