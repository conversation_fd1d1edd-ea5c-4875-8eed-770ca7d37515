import TrackProduct from '@components/TrackProduct';
import Quotes from '@components/exams/compare/Quotes';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import S7_ACHIEVABLE_VS_KAPLAN from '@constants/compare/finraSeries7AchievableVsKaplan';
import { ACHIEVABLE_VS_KAPLAN_S7_QUOTES } from '@constants/quotes';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const S7_SEO = SEO_PRODUCT['finra-series-7'];
const { sku } = S7_SEO;
const canonical = CANONICAL_URLS.examsFinraSeries7AchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated Series 7 courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords:
		'Achievable, Kaplan, FINRA Series 7, Series 7, Series 7 Exam Prep, best Series 7 prep, test prep, best Series 7 test prep, S7',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan Series 7 alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Pass your Series 7 on the first try with Achievable's up-to-date materials and modern study platform.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable Series 7's easy-to-understand material and adaptive learning platform gets you to pass in less time. These comparison stats come directly from the Kaplan Series 7 exam site.";

const STATS_DATA = generateStatsCard({ savings: 80 });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable S7'
			competitor='Kaplan S7'
			description={VERSUS_DESCRIPTION}
			seoProduct={S7_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={S7_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={S7_SEO.firstPage}
			rows={S7_ACHIEVABLE_VS_KAPLAN}
			size='cat'
			theme='versus'
		/>

		<Quotes competitor='Kaplan' course={S7_SEO.shortExamName} quotes={ACHIEVABLE_VS_KAPLAN_S7_QUOTES} />

		<IncludesEverything
			course={S7_SEO.shortExamName}
			href={S7_SEO.offerUrl}
			price={S7_SEO.priceF}
			product={S7_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S7_SEO.sku} />

			<FullLengthPracticeExams product={S7_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S7_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S7_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S7_SEO.sku} secondary />

			<ModernPlatform colorIndex={4} product={S7_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<PassGuaranteedCta
			exam={S7_SEO.fullExamName}
			href={S7_SEO.firstPage}
			detailText={S7_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
