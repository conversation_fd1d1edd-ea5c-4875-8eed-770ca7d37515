import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-series-6'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSeries6FreePracticeExam;

const seo = {
	canonical,
	description:
		'Take our free FINRA Series 6 practice exam and see if you can pass answering these Series 6 practice questions.',
	keywords:
		'series 6 practice exam, series 6 exam questions, series 6 sample questions, series 6 practice questions, series 6 questions, series 6 example questions, series 6 practice exam questions, finra series 6 practice exam',
	title: `Free Series 6 Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the Series 6 Exam`,
};

export const metadata = generatePageMetadata(seo);

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
