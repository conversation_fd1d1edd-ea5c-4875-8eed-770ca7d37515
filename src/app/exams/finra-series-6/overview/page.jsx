import TrackProduct from '@components/TrackProduct';
import SocialProof from '@components/exams/finra/SocialProof';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import AskAchievableAI from '@components/exams/shared/AskAchievableAI';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import HowToGet from '@components/exams/shared/HowToGet';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import NarratedAudio from '@components/exams/shared/NarratedAudio';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import PracticeExam from '@components/exams/shared/PracticeExam';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import WhatsCovered from '@components/exams/shared/WhatsCovered';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import Link from '@components/libraryV2/Link';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Resources from '@components/resources';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const S6_SEO = SEO_PRODUCT['finra-series-6'];

const { title, description, keywords, sku, canonical } = S6_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: S6_SEO.imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={S6_SEO.sku}
			title={
				<>
					Pass the <ThemeText noWrap>Series 6</ThemeText>
				</>
			}
			description={
				<div className={styles.description}>
					<div>
						Whether you&apos;re looking to become an Investment Company and Variable Contracts Products Representative
						or renewing a lapsed license, Achievable is the best and most effective Series 6 exam prep on the market.
					</div>
					<div>
						Achievable exam prep includes our online textbook, review questions, and full-length practice exams.
					</div>
				</div>
			}
			tag='FINRA SERIES 6 EXAM PREP'
		/>

		<Reviews />

		<SocialProof />

		<IncludesEverything
			course={S6_SEO.shortExamName}
			href={S6_SEO.offerUrl}
			price={S6_SEO.priceF}
			product={S6_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S6_SEO.sku} />

			<FullLengthPracticeExams product={S6_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S6_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S6_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S6_SEO.sku} secondary />

			<NarratedAudio colorIndex={4} product={S6_SEO.sku} reverse secondary />

			<AskAchievableAI colorIndex={5} product={S6_SEO.sku} secondary />

			<ModernPlatform colorIndex={6} product={S6_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<TryAchievableCta product={S6_SEO.sku} />

		<ProvenSuccess product='finra' secondary />

		<HowToGet product={S6_SEO.sku} reverse reverseBackground withBackground>
			<div>
				The first step to getting a Series 6 license is to pass the FINRA SIE Exam - or to have been grandfathered in by
				holding a FINRA representative-level registration on October 1, 2018. See our{' '}
				<Link theme='primary' to='/exams/finra-sie/overview/'>
					Achievable SIE
				</Link>{' '}
				program for more info.
			</div>
			<div>
				The second step to getting your Series 6 license is to be sponsored by a FINRA-registered firm or a
				self-regulatory organization (SRO), typically your employer. After that, all you need to do is study and pass
				the test.
			</div>
		</HowToGet>

		<WhatsCovered product={S6_SEO.sku} secondary>
			The Series 6 exam - the Investment Company and Variable Contracts Products Representative Qualification
			Examination (IR) - is designed to test the competency of an entry-level candidate to perform their job as an
			investment company and variable contracts products representative. Passing the Series 6 is required for
			representatives to sell these products.
		</WhatsCovered>

		<PracticeExam product={S6_SEO.sku} reverse />

		<ExamOverview cta={<TryAchievableCta product={S6_SEO.sku} secondary />} seoProduct={S6_SEO} reverse secondary />

		<Resources
			copy='Check out our free Series 6 cheat sheets / Series 6 dump sheets and podcasts to help you prepare for the FINRA Series 6 exam or for a quick refresher on the Series 6 fundamentals.'
			seoProduct={S6_SEO}
			secondary
		/>

		<AuthorSpotlight author='brandonRith' />

		<PassGuaranteedCta
			exam={S6_SEO.fullExamName}
			href={S6_SEO.firstPage}
			detailText="Achievable is the best Series 6 exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
