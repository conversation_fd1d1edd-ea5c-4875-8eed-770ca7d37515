import TrackProduct from '@components/TrackProduct';
import Quotes from '@components/exams/compare/Quotes';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import S6_ACHIEVABLE_VS_KAPLAN from '@constants/compare/finraSeries6AchievableVsKaplan';
import { ACHIEVABLE_VS_KAPLAN_S6_QUOTES } from '@constants/quotes';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const S6_SEO = SEO_PRODUCT['finra-series-6'];

const { sku } = S6_SEO;

const canonical = CANONICAL_URLS.examsFinraSeries6AchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated Series 6 courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords:
		'Achievable, Kaplan, FINRA Series 6, Series 6, Series 6 Exam Prep, best Series 6 prep, test prep, best Series 6 test prep, S6',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan Series 6 alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Pass your ${S6_SEO.shortExamName} on the first try with Achievable's up-to-date materials and modern study platform.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable Series 6's easy-to-understand material and adaptive learning platform gets you to pass in less time. These comparison stats come directly from the Kaplan Series 6 exam site.";

const STATS_DATA = generateStatsCard({ savings: 30 });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable S6'
			competitor='Kaplan S6'
			description={VERSUS_DESCRIPTION}
			seoProduct={S6_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={S6_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={S6_SEO.firstPage}
			rows={S6_ACHIEVABLE_VS_KAPLAN}
			size='cat'
			theme='versus'
		/>

		<Quotes competitor='Kaplan' course={S6_SEO.shortExamName} quotes={ACHIEVABLE_VS_KAPLAN_S6_QUOTES} />

		<IncludesEverything
			course={S6_SEO.shortExamName}
			href={S6_SEO.offerUrl}
			price={S6_SEO.priceF}
			product={S6_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S6_SEO.sku} />

			<FullLengthPracticeExams product={S6_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S6_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S6_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S6_SEO.sku} secondary />

			<ModernPlatform colorIndex={4} product={S6_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<PassGuaranteedCta
			exam={S6_SEO.fullExamName}
			href={S6_SEO.firstPage}
			detailText={S6_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
