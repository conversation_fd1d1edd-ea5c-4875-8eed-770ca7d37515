import classNames from 'classnames';

import TrackProduct from '@components/TrackProduct';
import McatScoring from '@components/exams/mcat/McatScoring';
import { SidebarDesktop, SidebarMobile } from '@components/exams/mcat/Sidebar';
import Layout from '@components/layout';
import ContentIconRow from '@components/libraryV2/ContentIconRow';
import ContentWell from '@components/libraryV2/ContentWell';
import Heading from '@components/libraryV2/Heading';
import HeroStretch from '@components/libraryV2/HeroStretch';
import Link from '@components/libraryV2/Link';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const SEO = SEO_PRODUCT.mcat;
const MCAT_SEO = { ...SEO, canonical: CANONICAL_URLS.examsMcatFaq };
const { title, description, keywords, canonical, sku, imageUrl } = MCAT_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

// TODO: Add a link to " For more information, check out our list of top med schools and scores here. " when done
const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>MCAT</ThemeText> FAQs
					</>
				}
				titleTag='h1'
				description='Answers to the most common questions about the Medical College Admission Test.'
			/>

			<SidebarDesktop>
				<ContentIconRow
					iconClass='fa-regular fa-messages-question'
					title={
						<>
							How hard is the <ThemeText>MCAT</ThemeText>?
						</>
					}
					reverse
				>
					The MCAT is considered one of the most challenging graduate school exams due to its extensive length and
					question structure. The exam lasts approximately 7.5 hours, which includes two 10-minute breaks and a
					30-minute lunch break. All questions are passage-based, so there is a lot of information to decipher and
					various topics to reference.
				</ContentIconRow>

				<ContentWell secondary tighten>
					<Heading size='h2' tagType='h2'>
						When to take the <ThemeText>MCAT</ThemeText>?
					</Heading>
					<div className={classNames(styles.paragraph, styles.differ)}>
						<div>
							It is recommended to take the MCAT a year prior to enrolling in medical school. This allows for ample
							retesting time if needed and a proper amount of time to prepare for the test to be confident on exam day.
						</div>
						<div>
							It is also important to note that there are testing limitations. A test-taker can only test three times in
							the course of one calendar year and seven times total. All scores will be visible on medical school
							applications, so it is important to feel ready and prepared on exam day.
						</div>
					</div>
				</ContentWell>

				<ContentIconRow
					iconClass='fa-duotone fa-books'
					title={
						<>
							What is <ThemeText>MCAT CARS</ThemeText>?
						</>
					}
					reverseBackground
					withBackground
				>
					The section of the MCAT called CARS refers to Critical Analysis and Reasoning Skills. This is often referenced
					as the hardest section on the MCAT. However, it depends on the individual test-taker.
				</ContentIconRow>

				<ContentIconRow
					className={styles.paragraph}
					iconClass='fa-sharp-duotone fa-brain-circuit'
					title={
						<>
							What is the score range for the <ThemeText>MCAT</ThemeText> exam?
						</>
					}
					reverse
					secondary
				>
					<div>
						The MCAT is scored from 472 to 528, with an average score around 500. Each section is scored from 118 to
						132. The MCAT is not a pass/fail exam, and scores are ranked as a percentile among other test-takers in the
						same testing year.
					</div>
					<div>
						While there are other factors that play a role in medical school admissions, having a good MCAT score
						strongly improves admission chances.
					</div>
				</ContentIconRow>

				<McatScoring />

				<ContentIconRow
					iconClass='fa-duotone fa-building-columns'
					title={
						<>
							What scores are needed for top <ThemeText>medical schools</ThemeText>?
						</>
					}
					secondary
				>
					Top medical schools such as Harvard, Columbia, and Johns Hopkins are competitive due to their students'
					average MCAT scores. Research from previous years shows students' average scores ranged from 515 to 528. For
					more information, check out our list of top med schools and scores here.
				</ContentIconRow>

				<ContentIconRow
					iconClass='fa-regular fa-calendar-clock'
					title={
						<>
							When are <ThemeText>scores released</ThemeText>?
						</>
					}
					reverse
				>
					Scores are typically released about 30-35 days after the test date. The AAMC will also distribute scores to
					medical schools that have received applications from test-takers. You can view your scores by logging into
					your account through AAMC. For more information on MCAT scores, check the AAMC website{' '}
					<Link
						href='https://students-residents.aamc.org/mcat-scores/mcat-scores'
						rel='noopener noreferrer'
						target='_blank'
						theme='primary'
					>
						here
					</Link>
					.
				</ContentIconRow>

				<PassGuaranteedCta
					exam={MCAT_SEO.fullExamName}
					href={MCAT_SEO.firstPage}
					tagline={
						<>
							Hit your <ThemeText>target score</ThemeText>
						</>
					}
					noGuarantee
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
