import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/mcat/Sidebar';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import MCAT_PRACTICE_EXAM_ITEMS from '@constants/practice-exams/mcat';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata.ts';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT.mcat;
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsMcatFreePracticeExam;
const numQuestions = MCAT_PRACTICE_EXAM_ITEMS.length;

export const metadata = generatePageMetadata({
	canonical,
	description: 'Test your knowledge with a free MCAT practice exam. 15 questions with detailed explanations.',
	keywords: 'mcat practice exam, free mcat practice test, mcat questions',
	title: `Free MCAT Practice Exam (${getCurrentYear()})`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<SidebarMobile />
		<PracticeExamHero seoProduct={seoProduct} numQuestions={numQuestions} />
		<SidebarDesktop>
			<PracticeQuiz seoProduct={seoProduct} examOnly />
		</SidebarDesktop>
	</Layout>
);

export default Page;
