import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/mcat/Sidebar';
import Layout from '@components/layout';
import ExamOverview from '@components/libraryV2/ExamOverview';
import HeroStretch from '@components/libraryV2/HeroStretch';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const SEO = SEO_PRODUCT.mcat;
const MCAT_SEO = { ...SEO, canonical: CANONICAL_URLS.examsMcatExamInfo };
const { title, description, keywords, canonical, sku, imageUrl } = MCAT_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>MCAT</ThemeText> Exam information
					</>
				}
				titleTag='h1'
				description="What it is, what's tested, and how it's scored."
			/>

			<SidebarDesktop>
				<ExamOverview seoProduct={MCAT_SEO} outlineSecondary={false} summarySecondary={true} hideCta />

				<PassGuaranteedCta
					exam={MCAT_SEO.fullExamName}
					href={MCAT_SEO.firstPage}
					tagline={
						<>
							Hit your <ThemeText>target score</ThemeText>
						</>
					}
					noGuarantee={!MCAT_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
