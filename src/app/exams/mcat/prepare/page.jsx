import TrackProduct from '@components/TrackProduct';
import FullLengthPracticeExams from '@components/exams/mcat/FullLengthPracticeExams';
import { SidebarDesktop, SidebarMobile } from '@components/exams/mcat/Sidebar';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const MCAT_SEO = SEO_PRODUCT.mcat;
const { title, description, keywords, canonical = CANONICAL_URLS.examsMcatPrepare, imageUrl, sku } = MCAT_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<OverviewHero
				product={MCAT_SEO.sku}
				title={
					<>
						Ace your <ThemeText noWrap>MCAT exam</ThemeText>
					</>
				}
				description={
					<div className={styles.description}>
						<div>
							The Medical College Admission Test (MCAT) is the most widely recognized exam for medical school
							admissions. Achievable is the best and most effective MCAT prep on the market and the only MCAT prep
							course that uses memory science technology to ensure you pass on the first try.
						</div>
						<div>
							Achievable exam prep includes our complete online textbook, review questions, and full practice exams, all
							of which are easily accessible on your phone, tablet, or computer.
						</div>
					</div>
				}
				tag='MCAT EXAM PREP'
			/>

			<SidebarDesktop>
				<Reviews />

				<IncludesEverything
					course={MCAT_SEO.shortExamName}
					href={MCAT_SEO.offerUrl}
					price={MCAT_SEO.priceF}
					product={MCAT_SEO.sku}
				/>

				<FeatureSections>
					<AdvancedPersonalization product='mcat' secondary>
						Achievable MCAT prep uses adaptive learning techniques to create and update a personalized model of your
						memory, individually tracking your retention and mastery of each MCAT learning objective. Our learning
						engine monitors your study progress and continually adjusts your quiz questions to ensure you focus on the
						topics that matter most, improving study effectiveness and reducing overall study time.
					</AdvancedPersonalization>

					<FullLengthPracticeExams />

					<ReviewQuizzes colorIndex={2} product={MCAT_SEO.sku} reverseImage secondary />

					<OnlineTextbook product='mcat' colorIndex={3} reverse secondary>
						You'll see within minutes why our concise online textbook is a cut above other MCAT prep - it's easy to
						understand and written in plain English, filled with straightforward explanations that cut right to the
						chase. Achievable MCAT study materials are easy to read, mobile-friendly, and include detailed walkthroughs
						of sample questions. Our MCAT online course covers each of the exam sections in detail.
					</OnlineTextbook>

					<ModernPlatform colorIndex={4} product='mcat' reverseImage secondary>
						Whether studying on the web or on a smartphone, the Achievable MCAT prep platform UX is clean and
						responsive. Progress charts highlight your journey through the MCAT course content and your current strength
						in each section.
					</ModernPlatform>

					<AdaptiveStudyPlanner product='mcat' reverse secondary>
						Use our study planner tool to create a personalized day-by-day study plan and ensure you complete all your
						MCAT content by exam day. All you need to do is focus on studying, and our system will help you stay on
						track.
					</AdaptiveStudyPlanner>
				</FeatureSections>

				<ProvenSuccess product='mcat' />

				<PassGuaranteedCta
					exam={MCAT_SEO.fullExamName}
					href={MCAT_SEO.firstPage}
					tagline={
						<>
							Hit your <ThemeText>target score</ThemeText>
						</>
					}
					noGuarantee={!MCAT_SEO.hasGuarantee}
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
