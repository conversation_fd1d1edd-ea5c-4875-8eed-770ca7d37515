import TrackProduct from '@components/TrackProduct';
import Quotes from '@components/exams/compare/Quotes';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import FullLengthPracticeExams from '@components/exams/shared/FullLengthPracticeExams';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import ReviewQuizzes from '@components/exams/shared/ReviewQuizzes';
import VideosOnKeyTopics from '@components/exams/shared/VideosOnKeyTopics';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';

import S65_ACHIEVABLE_VS_KAPLAN from '@constants/compare/finraSeries65AchievableVsKaplan';
import { ACHIEVABLE_VS_KAPLAN_S65_QUOTES } from '@constants/quotes';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const S65_SEO = SEO_PRODUCT['finra-series-65'];
const { sku } = S65_SEO;
const canonical = CANONICAL_URLS.examsFinraSeries65AchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated Series 65 courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords:
		'Achievable, Kaplan, FINRA Series 65, Series 65, Series 65 Exam Prep, best Series 65 prep, test prep, best Series 65 test prep, S65',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan Series 65 alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Pass your ${S65_SEO.shortExamName} on the first try with Achievable's up-to-date materials and modern study platform.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable Series 65's easy-to-understand material and adaptive learning platform gets you to pass in less time. These comparison stats come directly from the Kaplan Series 65 exam site.";

const STATS_DATA = generateStatsCard({ savings: 120 });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable S65'
			competitor='Kaplan S65'
			description={VERSUS_DESCRIPTION}
			seoProduct={S65_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={S65_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={S65_SEO.firstPage}
			rows={S65_ACHIEVABLE_VS_KAPLAN}
			size='cat'
			theme='versus'
		/>

		<Quotes competitor='Kaplan' course={S65_SEO.shortExamName} quotes={ACHIEVABLE_VS_KAPLAN_S65_QUOTES} />

		<IncludesEverything
			course={S65_SEO.shortExamName}
			href={S65_SEO.offerUrl}
			price={S65_SEO.priceF}
			product={S65_SEO.sku}
		/>

		<FeatureSections>
			<AdvancedPersonalization product={S65_SEO.sku} />

			<FullLengthPracticeExams product={S65_SEO.sku} reverse secondary />

			<ReviewQuizzes colorIndex={2} product={S65_SEO.sku} reverseImage secondary />

			<OnlineTextbook product={S65_SEO.sku} colorIndex={3} reverse secondary />

			<VideosOnKeyTopics product={S65_SEO.sku} secondary />

			<ModernPlatform colorIndex={4} product={S65_SEO.sku} reverse reverseImage secondary />
		</FeatureSections>

		<PassGuaranteedCta
			exam={S65_SEO.fullExamName}
			href={S65_SEO.firstPage}
			detailText={S65_SEO.description}
			type='finra'
			secondary
		/>
	</Layout>
);

export default Page;
