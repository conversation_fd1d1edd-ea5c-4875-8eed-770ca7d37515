import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT['finra-series-65'];
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsFinraSeries65FreePracticeExam;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Take our free FINRA Series 65 practice exam and see if you can pass answering these Series 65 practice questions.',
	keywords:
		'series 65 practice exam, series 65 exam questions, series 65 sample questions, series 65 practice questions, series 65 questions, series 65 example questions, series 65 practice exam questions, finra series 65 practice exam',
	title: `Free Series 65 Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the Series 65 Exam`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
