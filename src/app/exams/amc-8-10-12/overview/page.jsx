import TrackProduct from '@components/TrackProduct';
import Aops from '@components/exams/amc-8-10-12/overview/Aops';
import CrucialFundamentals from '@components/exams/amc-8-10-12/overview/CrucialFundamentals';
import EasyToWorkWith from '@components/exams/amc-8-10-12/overview/EasyToWorkWith';
import KeyStats from '@components/exams/amc-8-10-12/overview/KeyStats';
import NoFluff from '@components/exams/amc-8-10-12/overview/NoFluff';
import UnparalleledRetention from '@components/exams/amc-8-10-12/overview/UnparalleledRetention';
import WhatsCovered from '@components/exams/amc-8-10-12/overview/WhatsCovered';
import WhyAmc from '@components/exams/amc-8-10-12/overview/WhyAmc';
import FeatureSections from '@components/exams/shared/FeatureSections';
import OverviewHero from '@components/exams/shared/OverviewHero';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ExamOverview from '@components/libraryV2/ExamOverview';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Typer from '@components/libraryV2/Typer';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const AMC8_SEO = SEO_PRODUCT['amc-8'];
const AMC12_SEO = SEO_PRODUCT['amc-12'];

const canonical = CANONICAL_URLS.examsAmcOverview;

const TYPER_ENTRIES = [
	'Stanford University',
	'Harvard University',
	'Yale University',
	'MIT',
	'CalTech',
	'Carnegie Mellon',
	'Princeton University',
	'Brown University',
	'UPenn',
	'Duke University',
];

export const metadata = generatePageMetadata({
	title: AMC12_SEO.title,
	description: AMC12_SEO.description,
	canonical,
	keywords: AMC12_SEO.keywords,
	image: {
		url: AMC12_SEO.imageUrl,
		width: 640,
		height: 640,
	},
});

// TODO handle this manually as there are two prodcts on this page
const amc8Schemas = generateCourseWebPageSchema({
	sku: AMC8_SEO.sku,
	canonical,
});

const amc12Schemas = generateCourseWebPageSchema({
	sku: AMC12_SEO.sku,
	canonical,
});

const schemas = amc8Schemas.concat(amc12Schemas);

const Page = () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={AMC8_SEO.sku} />
		<TrackProduct sku={AMC12_SEO.sku} />
		<OverviewHero
			buyItNowText='Buy AMC 8/10/12'
			peekInsideText='Preview AMC 8/10/12'
			product={AMC12_SEO.sku}
			title={
				<div className={styles.title}>
					<div>The first step to admission at</div>
					<div>
						<ThemeText>
							<Typer entries={TYPER_ENTRIES} stinger='Your Top Choice' />
						</ThemeText>
					</div>
				</div>
			}
			description={
				<div className={styles.description}>
					<div>
						Show off your skills by applying to your dream school with a great AMC score. Achievable is the only AMC
						exam prep course that uses memory science technology to ensure you reach your goal.
					</div>
					<div>
						Achievable&apos;s AMC courses are authored by Wes Carroll, who has been helping AIME hopefuls realize their
						ambitions for over two decades.
					</div>
				</div>
			}
			tag='AMC 8/10/12 EXAM PREP'
		/>

		<Reviews noun='students' />

		<FeatureSections>
			<CrucialFundamentals />

			<UnparalleledRetention reverse />

			<NoFluff colorIndex={2} reverseImage />

			<EasyToWorkWith colorIndex={3} reverse />
		</FeatureSections>

		<Aops />

		<TryAchievableCta product={AMC12_SEO.sku} secondary />

		<KeyStats product='amc-8' />
		<WhyAmc name='AMC 8' product='amc-8' withBackground />
		<WhatsCovered name='AMC 8' product='amc-8' reverse />

		<KeyStats product='amc-10' withBackground />
		<WhyAmc name='AMC 10' product='amc-10' />
		<WhatsCovered name='AMC 10' product='amc-10' reverse withBackground />

		<TryAchievableCta product={AMC12_SEO.sku} />

		<KeyStats product='amc-12' withBackground />
		<WhyAmc name='AMC 12' product='amc-12' />
		<WhatsCovered name='AMC 12' product='amc-12' reverse withBackground />

		<ExamOverview seoProduct={{ name: 'AMC 8/10/12', shortExamName: 'AMC', sku: 'amc-8-10-12' }} />

		<AuthorSpotlight author='wesCarroll' reverseBackground />

		<PassGuaranteedCta
			exam='AMC 8/10/12'
			href={AMC12_SEO.firstPage}
			tagline={
				<>
					Reach your <ThemeText>target score</ThemeText>
				</>
			}
			detailText="Achievable is the best online AMC exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
			type='amc'
			noGuarantee
		/>
	</Layout>
);

export default Page;
