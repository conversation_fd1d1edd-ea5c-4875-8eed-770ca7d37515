import TrackProduct from '@components/TrackProduct';
import FullLengthPracticeExams from '@components/exams/ptce/FullLengthPracticeExams';
import ReviewQuizzes from '@components/exams/ptce/ReviewQuizzes';
import { SidebarDesktop, SidebarMobile } from '@components/exams/ptce/Sidebar';
import AdaptiveStudyPlanner from '@components/exams/shared/AdaptiveStudyPlanner';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const PTCE_SEO = SEO_PRODUCT.ptce;
const { title, description, keywords, canonical = CANONICAL_URLS.examsPtcePrepare, imageUrl, sku } = PTCE_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }}>
			<TrackProduct sku={sku} />

			<SidebarMobile />

			<OverviewHero
				product={PTCE_SEO.sku}
				title={
					<>
						Pass the <ThemeText noWrap>PTCE exam</ThemeText>
					</>
				}
				description={
					<div className={styles.description}>
						<div>
							The Pharmacy Technician Certification Exam (PTCE) is the most widely recognized exam for becoming a
							Certified Pharmacy Technician (CPhT). Achievable is the best and most effective Pharmacy Tech prep on the
							market and the only PTCE exam prep course that uses memory science technology to ensure you pass on the
							first try.
						</div>
						<div>
							Achievable exam prep includes our complete online textbook, review questions, and full practice exams, all
							of which are easily accessible on your phone, tablet, or computer.
						</div>
					</div>
				}
				tag='PTCE EXAM PREP'
			/>

			<SidebarDesktop>
				<Reviews />

				<IncludesEverything
					course={PTCE_SEO.shortExamName}
					href={PTCE_SEO.offerUrl}
					price={PTCE_SEO.priceF}
					product={PTCE_SEO.sku}
				/>

				<FeatureSections>
					<AdvancedPersonalization product='ptce' secondary>
						Achievable PTCE prep uses adaptive learning techniques to create and update a personalized model of your
						memory, individually tracking your retention and mastery of each PTCE learning objective. Our learning
						engine monitors your study progress and continually adjusts your quiz questions to ensure you're focusing on
						the topics that matter most for you, improving study effectiveness while reducing overall study time.
					</AdvancedPersonalization>

					<FullLengthPracticeExams />

					<ReviewQuizzes />

					<OnlineTextbook product='ptce' colorIndex={3} reverse secondary>
						You'll see within minutes why our concise online textbook is a cut above other PTCE prep - it's easy to
						understand and written in plain English, filled with straightforward explanations that cut right to the
						chase. Achievable PTCE study materials are easy to read, mobile-friendly, and include detailed walkthroughs
						of sample questions. Our PTCE online course covers each of the PTCE sections in detail.
					</OnlineTextbook>

					<ModernPlatform colorIndex={4} product='ptce' reverseImage secondary>
						Whether studying on the web or on a smartphone, the Achievable PTCE prep platform UX is clean and
						responsive. Progress charts highlight your journey through the PTCE course content and your current strength
						in each section.
					</ModernPlatform>

					<AdaptiveStudyPlanner product='ptce' reverse secondary>
						Use our study planner tool to create a personalized day-by-day study plan and ensure you complete all your
						PTCE content by exam day. All you need to do is focus on studying, and our system will help you stay on
						track.
					</AdaptiveStudyPlanner>
				</FeatureSections>

				<ProvenSuccess product='ptce' />

				<AuthorSpotlight author='sujata' secondary product='ptce'>
					Learn from the best
				</AuthorSpotlight>

				<PassGuaranteedCta
					exam={PTCE_SEO.shortExamName}
					href={PTCE_SEO.firstPage}
					detailText="Achievable is the best PTCE exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
					type='ptce'
					noGuarantee={!PTCE_SEO.hasGuarantee}
					secondary
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
