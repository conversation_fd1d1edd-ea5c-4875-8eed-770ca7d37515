import TrackProduct from '@components/TrackProduct';
import { SidebarDesktop, SidebarMobile } from '@components/exams/ptce/Sidebar';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import PTCE_PRACTICE_EXAM_ITEMS from '@constants/practice-exams/ptce';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT.ptce;
const { sku } = seoProduct;
const canonical = CANONICAL_URLS.examsPtceFreePracticeExam;
const numQuestions = PTCE_PRACTICE_EXAM_ITEMS.length;

export const metadata = generatePageMetadata({
	canonical,
	description: `Test your knowledge with a free PTCE practice exam. ${numQuestions} questions with detailed explanations.`,
	keywords: 'ptce practice exam, free ptce practice test, ptce questions',
	title: `Free PTCE Practice Exam (${getCurrentYear()})`,
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<SidebarMobile />
		<PracticeExamHero seoProduct={seoProduct} numQuestions={numQuestions} />
		<SidebarDesktop>
			<PracticeQuiz seoProduct={seoProduct} examOnly />
		</SidebarDesktop>
	</Layout>
);

export default Page;
