import classNames from 'classnames';

import TrackProduct from '@components/TrackProduct';
import PtcePassRates from '@components/exams/ptce/PtcePassRates';
import { SidebarDesktop, SidebarMobile } from '@components/exams/ptce/Sidebar';
import Layout from '@components/layout';
import ContentIconRow from '@components/libraryV2/ContentIconRow';
import ContentWell from '@components/libraryV2/ContentWell';
import Heading from '@components/libraryV2/Heading';
import HeroStretch from '@components/libraryV2/HeroStretch';
import Link from '@components/libraryV2/Link';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const SEO = SEO_PRODUCT.ptce;
const PTCE_SEO = { ...SEO, canonical: CANONICAL_URLS.examsPtceFaq };
const { title, description, keywords, canonical, sku, imageUrl } = PTCE_SEO;

export const metadata = generatePageMetadata({
	title,
	description,
	keywords,
	canonical,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => {
	return (
		<Layout jsonLd={{ schemas }} secondary>
			<TrackProduct sku={sku} />
			<SidebarMobile />

			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						<ThemeText>PTCE</ThemeText> FAQs
					</>
				}
				titleTag='h1'
				description='Answers to the most common questions about the PTCE.'
			/>

			<SidebarDesktop>
				<ContentIconRow
					iconClass='fa-duotone fa-pills'
					title={
						<>
							What can you do with a <ThemeText>Pharmacy Technician Certification</ThemeText>?
						</>
					}
					reverse
				>
					With a Pharmacy Technician Certification (CPhT), individuals can work in various healthcare settings, such as
					retail pharmacies, hospitals, and long-term care facilities, assisting pharmacists in dispensing medications,
					managing inventory, and providing customer service. Additionally, certification may lead to higher job
					prospects, increased earning potential, and opportunities for career advancement within the pharmacy
					profession.
				</ContentIconRow>

				<ContentWell secondary tighten>
					<Heading size='h2' tagType='h2'>
						How do I get a <ThemeText>PTCB Certification</ThemeText>?
					</Heading>
					<div className={classNames(styles.paragraph, styles.differ)}>
						<div>
							To obtain a{' '}
							<Link
								href='https://www.ptcb.org/guidebook/ptcb-certified-pharmacy-technician-cpht-program'
								rel='noopener noreferrer'
								target='_blank'
								theme='primary'
							>
								Pharmacy Technician Certification Board (PTCB) certification
							</Link>
							, which requires passing the Pharmacy Technician Certification Exam (PTCE), there are several steps and
							requirements you must fulfill:
						</div>
						<div className={styles.numberedList}>
							<div>
								1. You must reside in the United States or its territories, as the certification is specific to this
								region.
							</div>
							<div>
								2. You are required to complete a{' '}
								<Link
									href='https://www.ptcb.org/educators/ptcb-recognized-education-training-program-directory'
									rel='noopener noreferrer'
									target='_blank'
									theme='primary'
								>
									PTCB-Recognized Education/Training Program
								</Link>
								. Alternatively, if you have{' '}
								<Link
									href='https://www.ptcb.org/lib24watch/files/pdf/169'
									rel='noopener noreferrer'
									target='_blank'
									theme='primary'
								>
									equivalent
								</Link>{' '}
								work experience of a minimum of 500 hours, that can also satisfy this requirement on a{' '}
								<Link
									href='https://ptcb.zendesk.com/hc/en-us/articles/22946291754893-What-is-considered-work-experience'
									rel='noopener noreferrer'
									target='_blank'
									theme='primary'
								>
									case-by-case basis
								</Link>
								.
							</div>
							<div>
								3. You must fully disclose any criminal history and any actions taken by{' '}
								<Link
									href='https://nabp.pharmacy/about/boards-of-pharmacy/'
									rel='noopener noreferrer'
									target='_blank'
									theme='primary'
								>
									State Boards of Pharmacy
								</Link>{' '}
								regarding your registration or licensure. Failure to report criminal history can result in your
								certification being taken away.
							</div>
							<div>
								4. In addition to these steps, you must also comply with all{' '}
								<Link
									href='https://www.ptcb.org/guidebook/general-policies'
									rel='noopener noreferrer'
									target='_blank'
									theme='primary'
								>
									PTCB Certification policies
								</Link>
								.
							</div>
							<div>
								5. Finally, you must achieve a passing score on the Pharmacy Technician Certification Exam (PTCE).
							</div>
						</div>
						<div>
							By meeting these criteria and successfully passing the PTCE, you will earn your PTCB Certification.
						</div>
					</div>
				</ContentWell>

				<ContentIconRow
					iconClass='fa-duotone fa-clipboard-check'
					title={
						<>
							What is the difference between the <ThemeText>PTCE and ExCPT</ThemeText>?
						</>
					}
					reverseBackground
					withBackground
				>
					The PTCE (Pharmacy Technician Certification Exam) is administered by the Pharmacy Technician Certification
					Board (PTCB). In contrast, the ExCPT (Exam for the Certification of Pharmacy Technicians) is administered by
					the National Healthcareer Association (NHA). Both exams assess the knowledge and skills of individuals seeking
					certification as pharmacy technicians, but their specific contents and costs differ. See{' '}
					<Link
						href='https://blog.achievable.me/cpht/ptce-vs-excpt-two-paths-to-becoming-a-certified-pharmacy-technician-cpht/'
						rel='noopener noreferrer'
						target='_blank'
						theme='primary'
					>
						our blog post
					</Link>{' '}
					for a full breakdown.
				</ContentIconRow>

				<ContentIconRow
					iconClass='fa-regular fa-calendar-clock'
					title={
						<>
							How often is the <ThemeText>PTCE</ThemeText> offered?
						</>
					}
					reverse
					secondary
				>
					The PTCE is typically offered year-round, and candidates can schedule their exam at a time and location that
					is convenient for them, based on test center availability.
				</ContentIconRow>

				<ContentIconRow
					iconClass='fa-duotone fa-repeat'
					title={
						<>
							How many times can I retake the <ThemeText>PTCE</ThemeText>?
						</>
					}
					secondary
				>
					You can retake the PTCE as many times as you need; however, there is a six-month waiting period between your
					third and fourth attempts. After your fourth attempt, you must submit proof of acceptable preparation
					activities to the PTCB to retake the test.{' '}
					<Link
						href='https://www.ptcb.org/guidebook/after-the-exam'
						rel='noopener noreferrer'
						target='_blank'
						theme='primary'
					>
						Review the PTCB's exam retake policies
					</Link>
					.
				</ContentIconRow>

				<PtcePassRates />

				<PassGuaranteedCta
					exam={PTCE_SEO.shortExamName}
					href={PTCE_SEO.firstPage}
					detailText="Achievable is the best PTCE exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
					type='ptce'
				/>
			</SidebarDesktop>
		</Layout>
	);
};

export default Page;
