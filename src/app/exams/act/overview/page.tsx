import TrackProduct from '@components/TrackProduct';
import ComprehensiveEnglish from '@components/exams/act/overview/ComprehensiveEnglish';
import EndlessMathPractice from '@components/exams/act/overview/EndlessMathPractice';
import HowHardIsIt from '@components/exams/act/overview/HowHardIsIt';
import SmartReading from '@components/exams/act/overview/SmartReading';
import SoundScience from '@components/exams/act/overview/SoundScience';
import { ActPassGuaranteedCta } from '@components/exams/act/shared';
import AdvancedPersonalization from '@components/exams/shared/AdvancedPersonalization';
import FeatureSections from '@components/exams/shared/FeatureSections';
import ModernPlatform from '@components/exams/shared/ModernPlatform';
import OnlineTextbook from '@components/exams/shared/OnlineTextbook';
import OverviewHero from '@components/exams/shared/OverviewHero';
import ProvenSuccess from '@components/exams/shared/ProvenSuccess';
import Layout from '@components/layout';
import AuthorSpotlight from '@components/libraryV2/AuthorSpotlight';
import ContentIconRow from '@components/libraryV2/ContentIconRow';
import ExamOverview from '@components/libraryV2/ExamOverview';
import IncludesEverything from '@components/libraryV2/IncludesEverything';
import Reviews from '@components/libraryV2/Reviews';
import ThemeText from '@components/libraryV2/ThemeText';
import TryAchievableCta from '@components/libraryV2/TryAchievableCta';
import Typer from '@components/libraryV2/Typer';
import Resources from '@components/resources';

import SEO_PRODUCT from '@constants/seoProduct';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const ACT_SEO = SEO_PRODUCT.act;
const { title, description, keywords, sku, canonical, imageUrl } = ACT_SEO;

export const metadata = generatePageMetadata({
	canonical,
	title,
	description,
	keywords,
	image: {
		url: imageUrl,
		width: 640,
		height: 640,
	},
});

const TYPER_ENTRIES = [
	'Harvard University',
	'Brown University',
	'Stanford University',
	'Princeton University',
	'Yale University',
	'Columbia University',
	'University of Chicago',
	'MIT',
	'Caltech',
	'Duke University',
	'UPenn',
	'Dartmouth College',
];

const FEATURED_REVIEWS = [
	{
		rating: 5,
		quote:
			'This website is intuitive, clear, and comprehensive as it walks you through some of the more difficult terminologies along with proper explanations for them.',
		attribution: 'Ethan',
	},
	{
		rating: 5,
		quote:
			'Very simple to grasp concepts with well laid out formatting. Definitely makes getting through topics easy and efficient.',
		attribution: 'Steven',
	},
	{
		rating: 5,
		quote:
			"This is great. It has everything I need and everything is so intuitive and helpful it's just so so so so great.",
		attribution: 'Jason',
	},
];

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = () => (
	<Layout
		jsonLd={{
			schemas,
		}}
	>
		<TrackProduct sku={sku} />
		<OverviewHero
			product={ACT_SEO.sku}
			title={
				<div className={styles.title}>
					<div>The first step to admission at</div>
					<div>
						<ThemeText>
							<Typer entries={TYPER_ENTRIES} stinger='Your Top Choice' />
						</ThemeText>
					</div>
				</div>
			}
			description={
				<div className={styles.description}>
					<div>
						You know what score you need to apply to your dream school. Achievable is the only prep course for the ACT®
						test that uses memory science-enhanced practice to ensure you reach your goal.
					</div>
					<div className={styles.guarantee}>100% money back guarantee</div>
				</div>
			}
			disclaimer={
				<div className={styles.disclaimer}>
					ACT® is a registered trademark belonging to ACT Education Corp. ("ACT"). ACT is not involved with or
					affiliated with Achievable, nor does ACT endorse or sponsor any of the products or services offered by
					Achievable.
				</div>
			}
			tag='ACT EXAM PREP'
		/>

		<Reviews className={styles.reviews} noun='students' reviews={FEATURED_REVIEWS} />

		<IncludesEverything
			course={ACT_SEO.shortExamName}
			href={ACT_SEO.offerUrl}
			price={ACT_SEO.priceF}
			product={ACT_SEO.sku}
		/>

		<FeatureSections>
			<EndlessMathPractice reverse />

			<ComprehensiveEnglish reverse={false} />

			<SmartReading colorIndex={2} reverseImage reverse />

			<SoundScience colorIndex={3} reverse={false} />

			<ModernPlatform colorIndex={4} product='act' reverseImage reverse secondary />

			<OnlineTextbook product='act' secondary>
				Learn how to answer hard ACT questions in less time using proven strategies and pro tips from our expert author
				team with 10+ years of experience. Achievable&apos;s ACT course is easy to understand, mobile friendly, and
				includes detailed walkthroughs of practice questions. Achievable is the easiest way to learn the ACT math and
				English sections.
			</OnlineTextbook>

			<AdvancedPersonalization product='act' reverse />
		</FeatureSections>

		<TryAchievableCta product='act' />

		<ProvenSuccess product='act' secondary />

		<ContentIconRow
			iconClass='fa-regular fa-messages-question'
			title={
				<>
					Why take the <ThemeText>ACT</ThemeText>?
				</>
			}
			reverseBackground
			withBackground
			reverse
		>
			The ACT test is a nationally recognized college admissions test, just like the SAT exam. And like the SAT, it is
			accepted by <strong>all</strong> four-year colleges and universities in the US. The ACT is good value because, in
			addition to being a college admissions test, it also can count for college course placement, increase your chances
			of getting scholarships, and even includes a career planning section. Furthermore, while the SAT has undergone
			significant changes over the last few years, the ACT has remained the same. The ACT is the nation&apos;s
			most-taken college entrance test, and is based on what you already learned in school.
		</ContentIconRow>

		<ContentIconRow
			iconClass='fa-sharp fa-light fa-diagram-venn'
			title={
				<>
					Is the <ThemeText>ACT or SAT</ThemeText> more difficult?
				</>
			}
			secondary
		>
			In terms of the tests themselves, the ACT and SAT are similar in difficulty. But in terms of what score you need
			to get into good schools, the ACT has less competition. You need an ACT score in the top 3% to have a good shot of
			being admitted to Harvard or Yale, but that&apos;s actually more generous than the SAT, where you need a top 2%
			score for the same universities.
		</ContentIconRow>

		<ContentIconRow
			iconClass='fa-light fa-building-columns'
			title={
				<>
					Do colleges prefer the <ThemeText>SAT or ACT</ThemeText>?
				</>
			}
			reverseBackground
			withBackground
			reverse
		>
			The vast majority of colleges accept scores from either test. If they accept scores from both tests, they must
			evaluate them equally - this is required by law.
		</ContentIconRow>

		<HowHardIsIt />

		<ExamOverview seoProduct={ACT_SEO} />

		<Resources
			copy='Achievable has you covered for the ACT. Check out our Shift podcast to help you prepare for the ACT exam or for a quick refresher on college admissions and the ACT fundamentals.'
			seoProduct={ACT_SEO}
		/>

		<AuthorSpotlight author='marcGray' reverseBackground />

		<ActPassGuaranteedCta secondary={false} />
	</Layout>
);

export default Page;
