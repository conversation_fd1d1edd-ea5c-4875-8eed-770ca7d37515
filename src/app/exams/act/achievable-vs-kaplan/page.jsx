import TrackProduct from '@components/TrackProduct';
import { ActFeatureSections, ActPassGuaranteedCta } from '@components/exams/act/shared';
import Table from '@components/exams/compare/Table';
import VersusHero from '@components/exams/compare/VersusHero';
import { generateStatsCard } from '@components/exams/compare/VersusHero/callouts';
import Layout from '@components/layout';
import IncludesEverything from '@components/libraryV2/IncludesEverything';

import ACT_COMPARE from '@constants/compare/actAchievableVsKaplan';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const ACT_SEO = SEO_PRODUCT.act;

const { sku } = ACT_SEO;
const canonical = CANONICAL_URLS.examsActAchievableVsKaplan;

export const metadata = generatePageMetadata({
	canonical,
	description:
		'Compare two of the top-rated ACT courses — Achievable and Kaplan — to help you decide which study platform is right for you.',
	keywords: 'Achievable, Kaplan, ACT, ACT Exam Prep, best ACT prep, test prep, best ACT test prep',
	title: 'Kaplan vs Achievable - Why Achievable is the best Kaplan ACT alternative',
});

const VERSUS_DESCRIPTION = `Study like it's ${getCurrentYear()}. Improve your ACT score with Achievable's modern study platform and practice questions for math, English, science, reading, and writing.`;

const TABLE_DESCRIPTION =
	"Not just another course: Achievable ACT's easy-to-understand material and adaptive learning platform helps you improve your score in less time. These comparison stats come directly from the Kaplan ACT exam site.";

const PASS_RATE = {
	iconClass: 'fa-regular fa-award',
	stat: '95%+',
	subtitle: 'successful',
	title: 'Industry-best success rates',
};

const STATS_DATA = generateStatsCard({ savings: 30, customPassRate: PASS_RATE });

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Compare = () => (
	<Layout jsonLd={{ schemas }}>
		<TrackProduct sku={sku} />
		<VersusHero
			achievable='Achievable ACT'
			competitor='Kaplan ACT'
			description={VERSUS_DESCRIPTION}
			seoProduct={ACT_SEO}
			statsData={STATS_DATA}
		/>

		<Table
			course={ACT_SEO.shortExamName}
			description={TABLE_DESCRIPTION}
			href={ACT_SEO.firstPage}
			rows={ACT_COMPARE}
			size='cat'
			theme='versus'
		/>

		<IncludesEverything
			course={ACT_SEO.shortExamName}
			href={ACT_SEO.offerUrl}
			price={ACT_SEO.priceF}
			product={ACT_SEO.sku}
		/>

		<ActFeatureSections />

		<ActPassGuaranteedCta secondary />
	</Layout>
);

export default Compare;
