import TrackProduct from '@components/TrackProduct';
import Layout from '@components/layout';
import PracticeExamHero from '@components/libraryV2/PracticeExamHero';
import PracticeQuiz from '@components/libraryV2/PracticeQuiz';

import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

const seoProduct = SEO_PRODUCT.act;
const { sku } = seoProduct;

const canonical = CANONICAL_URLS.examsActFreePracticeExam;

const seo = {
	canonical,
	description:
		'Take our free ACT Math practice exam and see if you can pass answering these ACT Math practice questions.',
	keywords:
		'act math practice exam, act math exam questions, act math sample questions, act math practice questions, act math questions, act math example questions, act math practice exam questions, act practice exam, act math practice test, act math test questions, act math practice test questions, act practice test',
	title: `Free ACT Math Practice Exam (${getCurrentYear()}) with explanations | Practice Questions for the ACT Math Exam`,
};

export const metadata = generatePageMetadata(seo);

const schemas = generateCourseWebPageSchema({
	sku,
	canonical,
});

const Page = async () => (
	<Layout jsonLd={{ schemas }} secondary>
		<TrackProduct sku={sku} />
		<PracticeExamHero seoProduct={seoProduct} />
		<PracticeQuiz seoProduct={seoProduct} />
	</Layout>
);

export default Page;
