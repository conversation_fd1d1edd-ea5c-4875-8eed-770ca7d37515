import NextStep from '@components/finra-exams-securities-courses/NextStep';
import Sections from '@components/finra-exams-securities-courses/Sections';
import Layout from '@components/layout';
import ContentWell from '@components/libraryV2/ContentWell';
import HeroStretch from '@components/libraryV2/HeroStretch';
import Link from '@components/libraryV2/Link';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import { TableOfContentsContainer } from '@components/libraryV2/TableOfContents';
import TableOfContentsMobile from '@components/libraryV2/TableOfContentsMobile';
import ThemeText from '@components/libraryV2/ThemeText';

import FINRA_SERIES_EXAMS from '@constants/finraSeriesExams';
import SEO_PRODUCT from '@constants/seoProduct';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

import styles from './styles.module.scss';

const SIE_SEO = SEO_PRODUCT['finra-sie'];

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.finraExamsSecuritiesCourses,
	description:
		"Looking to get a high-paying job in finance? Then you'll need to pass FINRA's qualifying exams - we've got the key details and pass rates for every FINRA exam.",
	keywords: 'finra series exams, securities courses, securities training, achievable, test prep',
	title: 'FINRA Series exams and pass rates (complete guide)',
});

const tableOfContentsData = FINRA_SERIES_EXAMS.reduce((acc, section) => {
	const { uuid, title, exams = [] } = section;
	return acc.concat([
		{
			content: title,
			slug: uuid,
		},
		...exams.map((content) => {
			return {
				content: content.title,
				slug: content.uuid,
				level: 1,
			};
		}),
	]);
}, []);

const Description = () => (
	<div className={styles.description}>
		<div>
			Thinking of getting a{' '}
			<Link
				href='https://blog.achievable.me/careers-in-finance/finance-career-paths/'
				target='_blank'
				rel='noreferrer noopener'
			>
				job in the finance industry
			</Link>
			? It&apos;s not surprising as it is{' '}
			<Link
				href='https://blog.achievable.me/finra/securities-industry-essentials-sie-exam/how-much-money-can-you-make-in-finance-with-finance-salary-infographics/'
				target='_blank'
				rel='noreferrer noopener'
			>
				one of the most high-paying jobs
			</Link>{' '}
			you could have in the United States. But if you plan to delve into the world of securities and insurances, you
			must pass the qualifying exams administered by FINRA, which will allow you to engage in those areas of the
			industry.
		</div>
		<div>
			FINRA doesn&apos;t publish pass rate data for every exam, so we&apos;ve done our best to research and consolidate
			all the data we could find. The exact pass rates might be a little different from what we&apos;ve listed here, but
			you can feel confident that they&apos;ll be close.
		</div>
		<div>
			The following is a list of ALL the exams administered by FINRA with all the details you need to get the right
			securities training before registering to take the exam. Let&apos;s get started.
		</div>
	</div>
);

const FinraExams = () => {
	return (
		<Layout banner={<TableOfContentsMobile data={tableOfContentsData} />}>
			<HeroStretch
				className={styles.hero}
				description={<Description />}
				title={
					<div className={styles.title}>
						<ThemeText className={styles.noWrap}>FINRA Series exams</ThemeText> and pass rates
					</div>
				}
				titleTag='h1'
				subtitle='Complete guide'
			/>

			<ContentWell size='dog' tighten>
				<TableOfContentsContainer data={tableOfContentsData}>
					<Sections />
				</TableOfContentsContainer>
			</ContentWell>

			<NextStep />

			<PassGuaranteedCta
				exam='FINRA SIE'
				href={SIE_SEO.firstPage}
				detailText="Achievable is the best SIE exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and pass your exam the first time, or your money back."
				type='finra'
				secondary
			/>
		</Layout>
	);
};

export default FinraExams;
