import { notFound } from 'next/navigation';

import CltColleges from '@components/articles/CltColleges';

import { getAllCltCollegesByState } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';

export const revalidate = 86400;

export const generateMetadata = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const { slug } = await params;

	const allCltCollegesByState = await getAllCltCollegesByState();
	const stateData = allCltCollegesByState.find((o) => o.slug === slug);

	if (!stateData) notFound();

	const { state, colleges } = stateData;
	const canonical = `/articles/clt-colleges/${slug}/`;
	const title = `CLT Colleges in ${state}`;
	const description = `Read our comprehensive guide to CLT colleges in ${state}, including acceptance rates, average SAT and ACT scores, and school details.`;
	const collegeNames = colleges.map((college: any) => college.collegeName);
	const keywords = collegeNames.join(', ');

	const metadata = generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});

	return metadata;
};

export function generateStaticParams() {
	return [];
}

const Page = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const { slug } = await params;

	const allCltCollegesByState = await getAllCltCollegesByState();
	const stateData = allCltCollegesByState.find((o) => o.slug === slug);

	if (!stateData) notFound();

	const { state, colleges } = stateData;

	return <CltColleges colleges={colleges} state={state} states={allCltCollegesByState} />;
};

export default Page;
