import CltColleges from '@components/articles/CltColleges';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import { getAllCltCollegesByState } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';

export const generateMetadata = async () => {
	const allCltCollegesByState = await getAllCltCollegesByState();

	const canonical = CANONICAL_URLS.articlesCltColleges;
	const title = 'CLT Colleges';
	const description =
		'Read our comprehensive guide to CLT colleges, including acceptance rates, average SAT and ACT scores, and school details.';

	const colleges = allCltCollegesByState.flatMap((o) => o.colleges);
	const collegeNames = colleges.map((college: any) => college.collegeName);
	const keywords = collegeNames.join(', ');

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

const Page = async () => {
	const allCltCollegesByState = await getAllCltCollegesByState();
	const colleges = allCltCollegesByState.flatMap((o) => o.colleges);

	return <CltColleges colleges={colleges} states={allCltCollegesByState} isAllStates />;
};

export default Page;
