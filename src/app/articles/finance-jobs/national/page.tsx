import FinanceJobs from '@components/articles/FinanceJobs';

import { getAllFinanceCompaniesByName, getAllFinanceJobsByCity } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import { uniq } from '@lib/helpers';

export const revalidate = 86400;

export const generateMetadata = async () => {
	const allFinanceJobs = await getAllFinanceJobsByCity();

	const cities = [];
	const companies = [];
	for (const { city: jobCity, companies: jobCompanies } of allFinanceJobs) {
		cities.push(jobCity);
		companies.push(...jobCompanies);
	}

	const city = 'the United States';
	const companyNames = uniq(companies.map(({ companyName }) => `${companyName} finance jobs in ${city}`));
	const keywords = `finance jobs in ${city}, ${companyNames.join(', ')}`;

	const canonical = '/articles/finance-jobs/national/';
	const title = `Finance jobs in ${city}`;
	const description = `Read our curated list of the major finance firms in ${city}, including links to their website or careers page to help you see current openings.`;

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

const Page = async () => {
	const [allFinanceJobs, allFinanceCompanies] = await Promise.all([
		getAllFinanceJobsByCity(),
		getAllFinanceCompaniesByName(),
	]);

	const city = 'the United States';
	const companiesByCompanyName = allFinanceCompanies.reduce((acc, company) => {
		const { companyName } = company;
		acc[companyName] = company;
		return acc;
	}, {});

	const cities = [];
	const companies = [];
	for (const { city: jobCity, companies: jobCompanies } of allFinanceJobs) {
		cities.push(jobCity);
		companies.push(...jobCompanies);
	}

	return (
		<FinanceJobs
			cities={cities}
			city={city}
			companies={companies}
			companiesByCompanyName={companiesByCompanyName}
			isNational
		/>
	);
};

export default Page;
