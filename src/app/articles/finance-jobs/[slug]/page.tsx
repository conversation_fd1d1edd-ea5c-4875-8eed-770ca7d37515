import { notFound } from 'next/navigation';

import FinanceJobs from '@components/articles/FinanceJobs';

import { getAllFinanceCompaniesByName, getAllFinanceJobsByCity, getFinanceJobsByCity } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import { uniq } from '@lib/helpers';

export const revalidate = 86400;

interface Params {
	params: Promise<{ slug: string }>;
}

export const generateMetadata = async ({ params }: Params) => {
	const { slug } = await params;

	const financeJobsForCity = await getFinanceJobsByCity(slug);

	if (!financeJobsForCity) notFound();

	const { city, companies } = financeJobsForCity;
	const companyNames = uniq(companies.map(({ companyName }: any) => `${companyName} finance jobs in ${city}`));
	const keywords = `finance jobs in ${city}, ${companyNames.join(', ')}`;

	const canonical = `/articles/finance-jobs/${slug}/`;
	const title = `Finance jobs in ${city}`;
	const description = `Read our curated list of the major finance firms in ${city}, including links to their website or careers page to help you see current openings.`;

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

export function generateStaticParams() {
	return [];
}

const Page = async ({ params }: Params) => {
	const { slug } = await params;
	const [allFinanceJobs, allFinanceCompanies] = await Promise.all([
		getAllFinanceJobsByCity(),
		getAllFinanceCompaniesByName(),
	]);

	const jobCity = allFinanceJobs.find(({ slug: citySlug }) => citySlug === slug);

	if (!jobCity) notFound();

	const { companies, city } = jobCity;
	const cities = allFinanceJobs.map((o) => o.city);
	const companiesByCompanyName = allFinanceCompanies.reduce((acc, company) => {
		const { companyName } = company;
		acc[companyName] = company;
		return acc;
	}, {});

	return (
		<FinanceJobs cities={cities} city={city} companies={companies} companiesByCompanyName={companiesByCompanyName} />
	);
};

export default Page;
