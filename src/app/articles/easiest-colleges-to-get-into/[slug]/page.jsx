import { notFound } from 'next/navigation';
import pluralize from 'pluralize';

import RelatedUniversityPosts from '@components/articles/RelatedUniversityPosts';
import UniversityAcceptanceRates from '@components/articles/UniversityAcceptanceRates';
import Layout from '@components/layout';
import ArticleHero from '@components/libraryV2/ArticleHero';
import ArticleSubtitle from '@components/libraryV2/ArticleSubtitle';
import Card from '@components/libraryV2/Card';
import ContentWell from '@components/libraryV2/ContentWell';
import LinkBlock from '@components/libraryV2/LinkBlock';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import { TableOfContentsContainer } from '@components/libraryV2/TableOfContents';
import TableOfContentsMobile from '@components/libraryV2/TableOfContentsMobile';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { getStateUniversities, getTopUniversitiesByAcceptanceRateDesc } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import getLinksToOtherStates from '@lib/getLinksToOtherStates';
import getUniversitiesTableOfContentsData from '@lib/getUniversitiesTableOfContentsData';

import styles from '../styles.module.scss';

export const revalidate = 86400;

const ACT_SEO = SEO_PRODUCT.act;

const POST_SLUG = 'easiest-colleges-to-get-into';

export const generateMetadata = async ({ params }) => {
	const { slug } = await params;
	const stateUniversities = await getStateUniversities(slug);

	if (!stateUniversities) notFound();

	const { universities, state } = stateUniversities;
	const topUniversities = getTopUniversitiesByAcceptanceRateDesc({
		universities,
		num: 10,
	});
	const numUniversities = topUniversities.length;
	const universityNames = topUniversities.map((uni) => uni.schoolName).join(', ');
	const keywords = `easiest colleges to get into in ${state}, colleges with the highest acceptance rates in ${state}, high acceptance rates, ${state}, ${universityNames}`;

	const canonical = `/articles/${POST_SLUG}/${slug}/`;
	const title = `Easiest colleges to get into in ${state} | Top ${pluralize(
		'college',
		numUniversities,
		true
	)} in ${state} with the highest acceptance rates`;
	const description = `Find out the top ${pluralize(
		'easiest college',
		numUniversities,
		true
	)} to get into in ${state}. See how you measure up against the competition, and learn about other options.`;

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

export function generateStaticParams() {
	return [];
}

const Page = async ({ params }) => {
	const { slug } = await params;
	const stateUniversities = await getStateUniversities(slug);

	if (!stateUniversities) notFound();

	const { universities, state } = stateUniversities;
	const topUniversities = getTopUniversitiesByAcceptanceRateDesc({
		universities,
		num: 10,
	});
	const numUniversities = topUniversities.length;

	const description = (
		<>
			Top {pluralize('college', numUniversities, true)} in {state} with the{' '}
			<ThemeText>highest acceptance rates</ThemeText>
		</>
	);

	const tableOfContentsData = getUniversitiesTableOfContentsData({
		type: 'acceptance rate',
		universities: topUniversities,
	});

	const linksToOtherStates = getLinksToOtherStates({
		slug: POST_SLUG,
		state,
	});

	const path = `/${POST_SLUG}/${slug}`;

	return (
		<Layout banner={<TableOfContentsMobile data={tableOfContentsData} />} secondary>
			<ArticleHero
				title={
					<>
						<ThemeText>Easiest colleges</ThemeText> to get into in {state}
					</>
				}
			/>

			<ContentWell size='dog' tighten>
				<Card className={styles.description}>
					<ArticleSubtitle tagType='h2'>{description}</ArticleSubtitle>
					<div>
						What are the easiest colleges to get into in {state}? We&apos;ve got you covered. We&apos;ve compiled a
						national college database and have created a list of the easiest colleges to get into in {state} below.
						These are the easiest 4 year colleges to get into in {state} that have the highest acceptance rates, and are
						more likely to accept low GPAs, GEDs, and everything in between. There are tons of good colleges below that
						accept more applicants, and we also share their average SAT and ACT scores so that you can see where
						you&apos;re most competitive. Read on to find out more.
					</div>
				</Card>

				<TableOfContentsContainer data={tableOfContentsData}>
					<UniversityAcceptanceRates universities={topUniversities} />
				</TableOfContentsContainer>

				<Card className={styles.conclusion}>
					<ArticleSubtitle id='conclusion' tagType='h2'>
						Conclusion
					</ArticleSubtitle>
					<div>
						You don&apos;t need to go to Stanford, Harvard, or any college like that to be a standout success - so many
						people have become successful after attending all kinds of colleges and universities. There are tons of
						great schools around the country that have high acceptance rates and provide great educations for students.
						While some of these schools may not be a name brand, they will still give you the skills you need to be
						successful in whatever field you choose. Consider these colleges, and remember that any college admission is
						a big accomplishment!
					</div>

					<RelatedUniversityPosts path={path} state={state} />
				</Card>

				<ArticleSubtitle id='links' tagType='h2'>
					Wondering about other <ThemeText>easiest colleges</ThemeText> to get into?
				</ArticleSubtitle>
				<LinkBlock data={linksToOtherStates} />
			</ContentWell>

			<PassGuaranteedCta
				tagline={
					<>
						Hit your <ThemeText>target score</ThemeText>
					</>
				}
				href={ACT_SEO.firstPage}
				detailText="Achievable is the best online ACT exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
				type='act'
				noGuarantee
			/>
		</Layout>
	);
};

export default Page;
