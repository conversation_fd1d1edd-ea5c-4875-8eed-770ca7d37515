import { notFound } from 'next/navigation';
import pluralize from 'pluralize';

import RelatedUniversityPosts from '@components/articles/RelatedUniversityPosts';
import UniversitySatScores from '@components/articles/UniversitySatScores';
import Layout from '@components/layout';
import ArticleHero from '@components/libraryV2/ArticleHero';
import ArticleSubtitle from '@components/libraryV2/ArticleSubtitle';
import Card from '@components/libraryV2/Card';
import ContentWell from '@components/libraryV2/ContentWell';
import LinkBlock from '@components/libraryV2/LinkBlock';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import { TableOfContentsContainer } from '@components/libraryV2/TableOfContents';
import TableOfContentsMobile from '@components/libraryV2/TableOfContentsMobile';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { getStateUniversities, getTopUniversitiesBySatScoreDesc } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import getLinksToOtherStates from '@lib/getLinksToOtherStates';
import getUniversitiesTableOfContentsData from '@lib/getUniversitiesTableOfContentsData';

import styles from '../styles.module.scss';

export const revalidate = 86400;

const ACT_SEO = SEO_PRODUCT.act;

const POST_SLUG = 'colleges-with-the-highest-sat-scores';

export const generateMetadata = async ({ params }) => {
	const { slug } = await params;
	const stateUniversities = await getStateUniversities(slug);

	if (!stateUniversities) notFound();

	const { universities, state } = stateUniversities;
	const topUniversities = getTopUniversitiesBySatScoreDesc({
		universities,
		num: 10,
	});
	const numUniversities = topUniversities.length;
	const universityNames = topUniversities.map((uni) => uni.schoolName).join(', ');

	const canonical = `/articles/${POST_SLUG}/${slug}/`;
	const description = `Find out the top ${pluralize(
		'college',
		numUniversities,
		true
	)} with the highest SAT scores in ${state}. See how you measure up against the competition, and learn about other options.`;
	const keywords = `colleges with the highest sat scores in ${state}, low sat scores, ${state}, ${universityNames}`;
	const title = `Colleges with the highest SAT scores in ${state} | Top ${pluralize(
		'college',
		numUniversities,
		true
	)} in ${state} with the highest SAT scores`;

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

export function generateStaticParams() {
	return [];
}

const Page = async ({ params }) => {
	const { slug } = await params;
	const stateUniversities = await getStateUniversities(slug);

	if (!stateUniversities) notFound();

	const { universities, state } = stateUniversities;
	const topUniversities = getTopUniversitiesBySatScoreDesc({
		universities,
		num: 10,
	});
	const numUniversities = topUniversities.length;
	const description = (
		<>
			Top {pluralize('college', numUniversities, true)} in {state} with the <ThemeText>highest SAT scores</ThemeText>
		</>
	);

	const tableOfContentsData = getUniversitiesTableOfContentsData({
		type: 'SAT scores',
		universities: topUniversities,
	});

	const linksToOtherStates = getLinksToOtherStates({
		slug: POST_SLUG,
		state,
	});

	const path = `/${POST_SLUG}/${slug}`;

	return (
		<Layout banner={<TableOfContentsMobile data={tableOfContentsData} />} secondary>
			<ArticleHero
				title={
					<>
						Colleges with the <ThemeText>highest SAT scores</ThemeText> in {state}
					</>
				}
			/>

			<ContentWell size='dog' tighten>
				<Card className={styles.description}>
					<ArticleSubtitle tagType='h2'>{description}</ArticleSubtitle>
					<div>
						Looking for the colleges with the highest SAT scores in {state}? Well you&apos;re in luck! We&apos;ve
						compiled a national college database and have created a list of the top{' '}
						{pluralize('university', numUniversities, true)} with the highest SAT scores in {state}. These are the
						schools whose applicants had the highest average SAT scores in {state}. And, since these tests are meant to
						determine academic prowess, they are arguably the schools with the most academically proficient students.
						You could even say these are the best colleges in {state}. We also include each college&apos;s ACT scores
						and acceptance rate so that you can see where you would have the easiest or hardest time getting in. Read on
						to find out more.
					</div>
				</Card>

				<TableOfContentsContainer data={tableOfContentsData}>
					<UniversitySatScores universities={topUniversities} />
				</TableOfContentsContainer>

				<Card className={styles.conclusion}>
					<ArticleSubtitle id='conclusion' tagType='h2'>
						Conclusion
					</ArticleSubtitle>
					<div>
						Getting into any college is an accomplishment, but these schools are arguably some of the most competitive
						to get into in {state}. You will want to be above the average SAT or ACT score, and have a strong GPA with
						good extracurriculars in order to apply for these programs. Be sure to talk to your counselor about whether
						these schools are right for you before doing the work to apply.
					</div>

					<RelatedUniversityPosts path={path} state={state} />
				</Card>

				<ArticleSubtitle id='links' tagType='h2'>
					Wondering about other colleges with the <ThemeText>highest SAT and ACT scores?</ThemeText>
				</ArticleSubtitle>
				<LinkBlock data={linksToOtherStates} />
			</ContentWell>

			<PassGuaranteedCta
				tagline={
					<>
						Hit your <ThemeText>target score</ThemeText>
					</>
				}
				href={ACT_SEO.firstPage}
				detailText="Achievable is the best online ACT exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
				type='act'
				noGuarantee
			/>
		</Layout>
	);
};

export default Page;
