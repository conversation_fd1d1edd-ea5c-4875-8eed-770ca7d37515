import TopGraduateSchools from '@components/articles/TopGraduateSchools';

import { getAllTopGraduateSchoolsByProgram } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';

export const generateMetadata = async () => {
	const allTopGraduateSchoolsByProgram = await getAllTopGraduateSchoolsByProgram();

	const schools = allTopGraduateSchoolsByProgram.flatMap((o) => o.schools);
	const schoolNames = schools.map((data) => `${data.school} ${data.program} graduate school`);
	const keywords = schoolNames.join(', ');

	const canonical = '/articles/top-graduate-schools/';
	const title = 'Top graduate schools';
	const description =
		'Read our comprehensive guide to the top graduate schools, including acceptance rates, average GRE scores, and school details.';

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

const Page = async () => {
	const allTopGraduateSchoolsByProgram = await getAllTopGraduateSchoolsByProgram();
	const programs = allTopGraduateSchoolsByProgram.map((o) => o.program);
	const schools = allTopGraduateSchoolsByProgram.flatMap((o) => o.schools);

	return <TopGraduateSchools programs={programs} schools={schools} isAllPrograms />;
};

export default Page;
