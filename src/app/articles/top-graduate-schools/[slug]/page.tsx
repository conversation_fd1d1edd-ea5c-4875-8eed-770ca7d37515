import { notFound } from 'next/navigation';

import TopGraduateSchools from '@components/articles/TopGraduateSchools';

import { getAllTopGraduateSchoolsByProgram } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';

export const revalidate = 86400;

export const generateMetadata = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const { slug } = await params;

	const allTopGraduateSchoolsByProgram = await getAllTopGraduateSchoolsByProgram();
	const programData = allTopGraduateSchoolsByProgram.find((o) => o.slug === slug);

	if (!programData) notFound();

	const { program, schools } = programData;
	const schoolNames = schools.map((data: any) => `${data.school} ${data.program} graduate school`);
	const keywords = schoolNames.join(', ');

	const canonical = `/articles/top-graduate-schools/${slug}/`;
	const title = `Top ${program} graduate schools`;
	const description = `Read our comprehensive guide to the top ${program} graduate schools, including acceptance rates, average GRE scores, and school details.`;

	return generatePageMetadata({
		canonical,
		description,
		keywords,
		title,
	});
};

export function generateStaticParams() {
	return [];
}

const Page = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const { slug } = await params;

	const allTopGraduateSchoolsByProgram = await getAllTopGraduateSchoolsByProgram();
	const programs = allTopGraduateSchoolsByProgram.map((o) => o.program);
	const programData = allTopGraduateSchoolsByProgram.find((o) => o.slug === slug);

	if (!programData) notFound();

	const { program, schools } = programData;

	return <TopGraduateSchools program={program} programs={programs} schools={schools} />;
};

export default Page;
