import { notFound } from 'next/navigation';
import pluralize from 'pluralize';

import RelatedUniversityPosts from '@components/articles/RelatedUniversityPosts';
import UniversitySatScores from '@components/articles/UniversitySatScores';
import Layout from '@components/layout';
import ArticleHero from '@components/libraryV2/ArticleHero';
import ArticleSubtitle from '@components/libraryV2/ArticleSubtitle';
import Card from '@components/libraryV2/Card';
import ContentWell from '@components/libraryV2/ContentWell';
import LinkBlock from '@components/libraryV2/LinkBlock';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import { TableOfContentsContainer } from '@components/libraryV2/TableOfContents';
import TableOfContentsMobile from '@components/libraryV2/TableOfContentsMobile';
import ThemeText from '@components/libraryV2/ThemeText';

import SEO_PRODUCT from '@constants/seoProduct';
import { getAllStateUniversities, getTopUniversitiesBySatScoreAsc } from '@data/helpers';
import generatePageMetadata from '@lib/generatePageMetadata';
import getLinksToOtherStates from '@lib/getLinksToOtherStates';
import getUniversitiesTableOfContentsData from '@lib/getUniversitiesTableOfContentsData';

import styles from '../styles.module.scss';

const ACT_SEO = SEO_PRODUCT.act;

const POST_SLUG = 'colleges-with-the-lowest-sat-scores';

export const generateMetadata = async () => {
	try {
		const allUniversitiesByState = await getAllStateUniversities();
		const allUniversities = allUniversitiesByState.reduce((acc, { universities }) => [...acc, ...universities], []);
		const topUniversities = getTopUniversitiesBySatScoreAsc({
			universities: allUniversities,
		});
		const numUniversities = topUniversities.length;
		const universityNames = topUniversities.map((uni) => uni.schoolName).join(', ');

		const canonical = `/articles/${POST_SLUG}/national/`;
		const description = `Find out the top ${pluralize(
			'college',
			numUniversities,
			true
		)} with the lowest SAT scores in the United States. See how you measure up against the competition, and learn about other options.`;
		const keywords = `colleges with the lowest sat scores in the United States, low sat scores, colleges in the United States, ${universityNames}`;
		const title = `Colleges with the lowest SAT scores in the United States | Top ${pluralize(
			'college',
			numUniversities,
			true
		)} in the United States with the lowest SAT scores`;

		return generatePageMetadata({
			canonical,
			description,
			keywords,
			title,
		});
	} catch {
		return notFound();
	}
};

const Page = async () => {
	try {
		const allUniversitiesByState = await getAllStateUniversities();
		const allUniversities = allUniversitiesByState.reduce((acc, { universities }) => [...acc, ...universities], []);
		const topUniversities = getTopUniversitiesBySatScoreAsc({
			universities: allUniversities,
		});
		const numUniversities = topUniversities.length;

		const description = (
			<>
				Top {pluralize('college', numUniversities, true)} in the United States with the{' '}
				<ThemeText>lowest SAT scores</ThemeText>
			</>
		);

		const tableOfContentsData = getUniversitiesTableOfContentsData({
			type: 'SAT scores',
			universities: topUniversities,
		});

		const linksToOtherStates = getLinksToOtherStates({
			slug: POST_SLUG,
			state: 'national',
		});

		const path = `/${POST_SLUG}/national`;

		return (
			<Layout banner={<TableOfContentsMobile data={tableOfContentsData} />} secondary>
				<ArticleHero
					title={
						<>
							Colleges with the <ThemeText>lowest SAT scores</ThemeText> in the United States
						</>
					}
				/>

				<ContentWell size='dog' tighten>
					<Card className={styles.description}>
						<ArticleSubtitle tagType='h2'>{description}</ArticleSubtitle>
						<div>
							Looking for the colleges with the lowest SAT scores in the United States? Well you&apos;re in luck!
							We&apos;ve compiled a national college database and have created a list of the top{' '}
							{pluralize('university', numUniversities, true)} with the lowest SAT scores in the United States below. If
							you are not a good test taker or worried about your test scores, this list is for you. These are the
							schools whose applicants had the lowest average SAT scores in the United States, which means that you can
							get into these colleges with a lower SAT score. We also include each college&apos;s ACT scores and
							acceptance rate so that you can see where you would have the easiest time getting in. Read on to find out
							more.
						</div>
					</Card>

					<TableOfContentsContainer data={tableOfContentsData}>
						<UniversitySatScores universities={topUniversities} />
					</TableOfContentsContainer>

					<Card className={styles.conclusion}>
						<ArticleSubtitle id='conclusion' tagType='h2'>
							Conclusion
						</ArticleSubtitle>
						<div>
							If you are worried about your test scores, we hope this list helped you figure out what schools in the
							United States are within reach. These are great programs that can teach you all of the skills you need to
							be successful in your profession. You don&apos;t need to be a whiz kid to get a great education and have a
							lucrative career - just make the most of the education opportunities you receive and the sky&apos;s the
							limit. Consider these colleges, and remember that any college admission is a big accomplishment!
						</div>

						<RelatedUniversityPosts path={path} content='the United States' state='national' />
					</Card>

					<ArticleSubtitle id='links' tagType='h2'>
						Wondering about other colleges with the <ThemeText>lowest SAT and ACT scores?</ThemeText>
					</ArticleSubtitle>
					<LinkBlock data={linksToOtherStates} />
				</ContentWell>

				<PassGuaranteedCta
					tagline={
						<>
							Hit your <ThemeText>target score</ThemeText>
						</>
					}
					href={ACT_SEO.firstPage}
					detailText="Achievable is the best online ACT exam prep course: effective, personalized, and convenient. With Achievable, you'll spend less time studying and hit your target score with confidence."
					type='act'
					noGuarantee
				/>
			</Layout>
		);
	} catch {
		return notFound();
	}
};

export default Page;
