import React from 'react';

import Layout from '@components/layout';
import ContentWell from '@components/libraryV2/ContentWell';
import FaqBlock from '@components/libraryV2/FaqBlock';
import Hero from '@components/libraryV2/Hero';
import LinkButton from '@components/libraryV2/LinkButton';
import ThemeText from '@components/libraryV2/ThemeText';

import FAQ_ITEMS from '@constants/faq';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

import styles from './styles.module.scss';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.faq,
	description: "Learn the answers to frequently asked questions about the FINRA SIE and Achievable's exam prep.",
	title: 'Frequently asked questions about the FINRA SIE',
	keywords: 'achievable, achievable sie, achievable gre, achievable usmle, achievable series 7',
});

const Description = () => (
	<div className={styles.description}>
		<div>Couldn&apos;t find an answer?</div>
		<LinkButton to='/contact/' theme='emerald'>
			Ask us
		</LinkButton>
	</div>
);

const FaqPage = () => {
	return (
		<Layout>
			<Hero
				title={
					<div>
						<div>Frequently asked</div> <ThemeText>Questions</ThemeText>
					</div>
				}
				description={<Description />}
				withBackground
			/>

			<ContentWell>
				<FaqBlock items={FAQ_ITEMS} />
			</ContentWell>
		</Layout>
	);
};

export default FaqPage;
