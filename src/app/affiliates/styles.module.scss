.description {
	margin-bottom: 16px;
}

.header {
	display: flex;
	flex-flow: column nowrap;
	align-items: center;
	margin: 0 auto;
}

.well {
	position: relative;
}

.heading {
	margin-bottom: 40px;
}

.cta {
	display: flex;
	flex-flow: column nowrap;
	gap: 16px;
	width: 100%;
	margin-top: 16px;
	color: #525962;
	color: var(--color-main--secondary);
}

.readyToJoin {
	max-width: max-content;
}

.program {
	display: block;
}

@media (max-width: 699px) {
	.header {
		align-items: flex-start;
	}

	.heading {
		margin-bottom: 24px;
	}
}

@media (max-width: 599px) {
	.program {
		display: inline-block;
	}

	.description {
		margin-bottom: 12px;
	}
}
