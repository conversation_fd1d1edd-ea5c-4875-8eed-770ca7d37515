import classNames from 'classnames';

import Benefits from '@components/affiliates/Benefits';
import Layout from '@components/layout';
import ContentWell from '@components/libraryV2/ContentWell';
import FaqBlock from '@components/libraryV2/FaqBlock';
import Heading from '@components/libraryV2/Heading';
import Hero from '@components/libraryV2/Hero';
import Link from '@components/libraryV2/Link';
import LinkButton from '@components/libraryV2/LinkButton';
import ThemeText from '@components/libraryV2/ThemeText';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const FAQS = [
	{
		question: 'How does affiliate marketing work?',
		answer:
			"When you join our affiliate program you're given a unique referral link. You're paid for qualified sales from the users you send our way.",
	},
	{
		question: 'How long does it take to get started?',
		answer:
			"You can get started immediately - signups are automatically approved. We'll reach out within a day or two after you sign up to see if you have any questions and if there's anything we can do to help you effectively market.",
	},
	{
		question: 'Is there a limit on the total commission I can receive?',
		answer: 'There are no limits. The more purchases you drive, the more you earn.',
	},
	{
		question: 'Do I have to pay to join the program?',
		answer: "No, it's completely free to be an affiliate partner.",
	},
	{
		question: 'What content will appear on my website?',
		answer:
			"You're in charge of your website and can choose how to promote Achievable courses. We'll provide you with a variety of professionally-designed text and banner links that you can use.",
	},
	{
		question: 'Is my website eligible?',
		answer:
			"Probably. We don't set strict eligibility criteria, but we do want to ensure that you're not doing anything illegal or that might negatively impact our brand. You can refer your customers or visitors, but you cannot refer yourself or trade referral codes with another user. We encourage and appreciate legitimate referrals, but as there are some people who abuse these programs, we reserve the right to reject and remove sites or commissions at any time, for any reason.",
	},
	{
		question: 'How are commissions paid?',
		answer:
			'Payments are made on a net 30 basis after the end of each month, via bank transfer, paypal, or other mutually agreed upon payment method.',
	},
	{
		question: 'How do I keep track of my sales and commissions?',
		answer:
			'Our affiliate platform is powered by Post Affiliate Pro, and you can see the clicks, free signups, and purchases driven by the traffic you send to us.',
	},
	{
		question: 'Can I use paid advertising to generate leads?',
		answer:
			'Yes, with some restrictions. You cannot participate in paid advertising on our branded terms, including any derivations, variations, or misspellings. Basically, you cannot advertise against any queries including "Achievable" or "Achievable.me" as a broad match.',
	},
	{
		question: 'Still have questions?',
		answer: (
			<>
				Email us at
				<Link href='mailto:<EMAIL>'><EMAIL></Link>
			</>
		),
	},
];

const seo = {
	canonical: CANONICAL_URLS.affiliates,
	description:
		'Partner with Achievable to generate new revenue from your website and help people achieve their professional goals',
	keywords:
		'achievable, exam prep, exam prep, FINRA SIE, FINRA series 7, gre, graduate record exam, usmle,  United States Medical Licensing Examination',
	title: 'Earn 20%+ as an Achievable affiliate partner',
};

export const metadata = generatePageMetadata(seo);

const webPageSchema = generateWebPageSchema(seo);

const schemas = [webPageSchema];

const JoinNow = () => (
	<LinkButton href='https://achievable.postaffiliatepro.com/affiliates/signup.php#SignupForm' theme='emerald'>
		Join now
	</LinkButton>
);

const Description = () => (
	<>
		<div className={styles.description}>
			Partner with Achievable to generate new revenue from your website and help people achieve their professional goals
		</div>
		<JoinNow />
	</>
);

const Affiliates = () => (
	<Layout jsonLd={{ schemas }}>
		<Hero
			title={
				<>
					Become an <ThemeText>Affiliate</ThemeText>
				</>
			}
			description={<Description />}
			withBackground
		/>

		<ContentWell size='dog' tighten>
			<div className={styles.header}>
				<Heading className={styles.heading} size='h2' tagType='h2'>
					Take <ThemeText>advantage</ThemeText> of what our program has to offer
				</Heading>
			</div>
			<Benefits />
		</ContentWell>

		<ContentWell className={styles.well} secondary>
			<div className={styles.header}>
				<Heading className={styles.heading} size='h2' tagType='h2'>
					Have <ThemeText>questions</ThemeText>? We have answers
				</Heading>
			</div>
			<FaqBlock items={FAQS} />
		</ContentWell>

		<ContentWell className={styles.well} reverseBackground withBackground>
			<div className={classNames(styles.header, styles.readyToJoin)}>
				<Heading size='h2' tagType='h2'>
					Ready to join our <ThemeText>affiliate</ThemeText> <span className={styles.program}>program?</span>
				</Heading>
				<div className={styles.cta}>
					<div>Start earning more from the traffic you already have</div>
					<JoinNow />
				</div>
			</div>
		</ContentWell>
	</Layout>
);

export default Affiliates;
