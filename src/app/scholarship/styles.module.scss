.layout {
	display: flex;
	flex-flow: column nowrap;
	align-items: center;
	justify-content: center;
	padding: 80px 8px 48px;
	position: relative;

	.header.card {
		padding: unset;
		margin-top: 40px;
	}

	.headerContent {
		display: flex;
		flex-flow: column nowrap;
		align-items: center;
		gap: 12px;
		position: relative;
		padding: 40px;

		.win {
			font-size: 32px;
			line-height: 32px;
			font-weight: 500;
		}
	}
}

.description {
	display: flex;
	flex-flow: column nowrap;
	gap: 8px;
	color: #525962;
	color: var(--color-main--secondary);
	max-width: 720px;
	margin-top: 24px;
	font-size: 14px;
	padding: 0 8px;
}

@media (max-width: 799px) {
	.layout {
		padding-top: 80px;

		.headerContent {
			padding: 24px;

			.win {
				font-size: 24px;
				line-height: 24px;
			}

			.monthly {
				font-size: 32px;
				line-height: 32px;
			}

			.scholarship {
				font-size: 24px;
				line-height: 24px;
			}
		}
	}

	.description {
		max-width: 500px;
	}
}

@media (max-width: 599px) {
	.layout {
		padding: 64px 8px 0px;

		.header {
			margin-top: 24px;
		}

		.headerContent {
			padding: 16px;

			.win {
				font-size: 20px;
				line-height: 20px;
			}

			.monthly {
				font-size: 24px;
				line-height: 24px;
			}

			.scholarship {
				font-size: 18px;
				line-height: 18px;
			}
		}
	}

	.description {
		max-width: 360px;
	}
}

@media (max-width: 499px) {
	.layout {
		padding: 48px 8px;

		.header {
			margin-top: 16px;
		}

		.headerContent {
			gap: 8px;

			.monthly {
				font-size: 22px;
				line-height: 22px;
			}
		}
	}
}
