import { notFound } from 'next/navigation';

import Counselors from '@components/directory/Counselors';
import ProductTutors from '@components/directory/ProductTutors';
import Profile from '@components/directory/Profile';

import getDirectoryData from '@handlers/getDirectoryData';
import { generateAwardsPageMetadata, handleAwardsPage } from '@handlers/handleAwardsPage';
import { generateCounselorsPageMetadata, handleCounselorPage } from '@handlers/handleCounselorsPage';
import { generateTutorStaticParams, generateTutorsPageMetadata, handleTutorPage } from '@handlers/handleTutorsPage';

export const revalidate = 86400;

interface Params {
	params: Promise<{ slug: string; page?: string[] }>;
}

export const generateMetadata = async ({ params }: Params) => {
	const { slug, page } = await params;
	const directoryData = await getDirectoryData(slug);

	if (!directoryData) notFound();

	const { type } = directoryData;

	switch (type) {
		case 'tutors': {
			const {
				data: { productSlug },
			} = directoryData;
			const tutorsPageMetadata = await generateTutorsPageMetadata({ productSlug, page });
			return tutorsPageMetadata;
		}
		case 'counselors': {
			const {
				data: { typeSlug },
			} = directoryData;
			const counselorsPageMetadata = await generateCounselorsPageMetadata({
				page,
				typeSlug,
			});
			return counselorsPageMetadata;
		}
		case 'awardsProfile': {
			const {
				data: { nomineeSlug },
			} = directoryData;
			const awardsProfileMetadata = await generateAwardsPageMetadata({
				nomineeSlug,
			});
			return awardsProfileMetadata;
		}
		default:
			notFound();
	}
};

export const generateStaticParams = async () => {
	const params = await generateTutorStaticParams();
	return params;
};

const Page = async ({ params }: Params) => {
	const { slug, page } = await params;
	const directoryData = await getDirectoryData(slug);

	if (!directoryData) notFound();

	const { type } = directoryData;

	/* eslint-disable react/jsx-props-no-spreading */
	switch (type) {
		case 'tutors': {
			const {
				data: { productSlug },
			} = directoryData;
			const tutorData = await handleTutorPage({ productSlug, page });
			const { isProfilePage, pageNumber, product } = tutorData;

			if (isProfilePage) {
				const { tutor } = tutorData;
				return <Profile page={pageNumber} product={product} profileType={type} {...tutor} />;
			}

			const { basePath, tutors, totalPages, meta } = tutorData;

			return (
				<ProductTutors
					basePath={basePath}
					meta={meta}
					page={pageNumber}
					product={product}
					totalPages={totalPages}
					tutors={tutors}
				/>
			);
		}

		case 'counselors': {
			const {
				data: { typeSlug },
			} = directoryData;
			const counselorData = await handleCounselorPage({
				page,
				typeSlug,
			});
			const { isProfilePage, pageNumber, product, type: counselorType } = counselorData;

			if (isProfilePage) {
				const { counselor } = counselorData;
				return <Profile type={counselorType} product={product} page={pageNumber} profileType={type} {...counselor} />;
			}

			const { basePath, counselors, totalPages } = counselorData;

			return (
				<Counselors
					basePath={basePath}
					type={counselorType}
					counselors={counselors}
					page={pageNumber}
					totalPages={totalPages}
				/>
			);
		}
		case 'awardsProfile': {
			const {
				data: { nomineeSlug },
			} = directoryData;
			const { event, nominee, nominations, backTo } = await handleAwardsPage({ nomineeSlug });
			return <Profile event={event} nominations={nominations} backTo={backTo} profileType={type} {...nominee} />;
		}
		default:
			notFound();
	}
};

export default Page;
