import React from 'react';

import NextImage from '@components/NextImage';
import Layout from '@components/layout';
import Card from '@components/libraryV2/Card';
import ContentImageRow from '@components/libraryV2/ContentImageRow';
import LinkButton from '@components/libraryV2/LinkButton';
import ThemeText from '@components/libraryV2/ThemeText';
import ActivityHero from '@components/resources/injured-student-athlete-activity-finder/ActivityFinder/ActivityHero';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

import loveyReylonds1Img from '@images/misc/lovey-1.png';
import loveyReylonds2Img from '@images/misc/lovey-2.png';

import styles from './styles.module.scss';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.resourcesInjuredStudentAthleteActivityFinderAbout,
	description:
		'About page describing the development of the Student Athlete Activity Finder and its creator <PERSON><PERSON>',
	keywords:
		'sports injury, student athlete, activity finder, sports, injury, student, athlete, hobby finder, hobbies, activities',
	title: 'About the Student Athlete Activity Finder and <PERSON><PERSON>',
});

const ImageContainer = ({ children }) => <Card className={styles.image}>{children}</Card>;

const AboutLovey = () => (
	<Layout>
		<ActivityHero />

		<ContentImageRow
			title={
				<>
					About this quiz and <ThemeText>Lovey Reynolds</ThemeText>
				</>
			}
			image={
				<ImageContainer>
					<NextImage alt='Lovey Reynolds' src={loveyReylonds2Img} height={380} />
				</ImageContainer>
			}
			size='dog'
			secondary
		>
			<div>
				My name is Lovey Reynolds. The Student Athlete Activity Finder allows student athletes to find hobbies that
				interest them to temporarily replace the time their sport took up in their lives. This app uses Holland&apos;s
				Theory of Career Choice. The Holland Career Choice framework specializes in matching one&apos;s personality with
				specific jobs and careers. However, the results of the Student Athlete Activity Finder will not show you
				potential careers.
			</div>
			<div>
				Instead, the Student Athlete Activity Finder gives you a list of different activities that are broken into
				categories that best fit you and your personality. These categories include the following: Investigative,
				Artistic, Realistic, Conventional, Social, and Enterprising. Results will display these categories and highlight
				suggested activities including information on mobility level, the main body part used, and if the activity is
				more relaxing or challenging.
			</div>
		</ContentImageRow>

		<ContentImageRow
			title={
				<>
					<ThemeText>My injury</ThemeText> experience
				</>
			}
			image={
				<ImageContainer>
					<NextImage alt='Lovey Reynolds' src={loveyReylonds1Img} width={267} />
				</ImageContainer>
			}
			size='dog'
			reverse
		>
			<div>
				There&apos;s a reason I have such a keen interest in this program. In 2019, I had a significant back injury that
				took me out of the sport I love - tennis. I was unable to play for two years and missed playing every single
				day. In 2021, my back had healed but I was involved in yet another accident that left me with a severe
				concussion and nerve damage in my leg. Once again, I was forced to quit the sport I love playing and focus on
				recovering.
			</div>
			<div>
				Since I practiced every day, I found myself with too much time on my hands and needed something positive to do
				with my time while I was healing and working to recover. Physical therapy and yoga therapy was just not enough!
				During my recovery, I encountered many student athletes like myself who were suffering and bored with the extra
				time on their hands. This inspired me to want to help others find alternative activities that can fill the time
				and void left when an athlete is forced to step away from their sport.
			</div>
			<div className={styles.buttonContainer}>
				<LinkButton to='/resources/injured-student-athlete-activity-finder/' secondary>
					Back to quiz
				</LinkButton>
			</div>
		</ContentImageRow>
	</Layout>
);

export default AboutLovey;
