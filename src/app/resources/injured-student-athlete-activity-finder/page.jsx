import React from 'react';

import Layout from '@components/layout';
import ContentWell from '@components/libraryV2/ContentWell';
import ActivityFinder from '@components/resources/injured-student-athlete-activity-finder/ActivityFinder';
import ActivityHero from '@components/resources/injured-student-athlete-activity-finder/ActivityFinder/ActivityHero';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.resourcesInjuredStudentAthleteActivityFinder,
	description:
		'The Student Athlete Activity Finder quiz provides temporary recommendations of activities to replace the sport the injured athlete competed in. The quiz presents a series of general questions followed by questions meant to determine your interests.',
	keywords:
		'sports injury, student athlete, activity finder, sports, injury, student, athlete, hobby finder, hobbies, activities',
	title: 'Student Athlete Activity Finder',
});

const LoveyWidget = () => {
	return (
		<Layout secondary>
			<ActivityHero />
			<ContentWell secondary>
				<ActivityFinder />
			</ContentWell>
		</Layout>
	);
};

export default LoveyWidget;
