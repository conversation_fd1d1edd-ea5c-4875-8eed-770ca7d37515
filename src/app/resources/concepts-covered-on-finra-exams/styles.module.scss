.description {
	display: flex;
	flex-flow: column nowrap;
	gap: 24px;
	color: #525962;
	color: var(--color-main--secondary);
}

.content {
	display: flex;
	flex-flow: column nowrap;
	gap: 64px;
}

.faq {
	margin-top: 24px;
}

.faqItem {
	display: flex;
	flex-flow: column nowrap;
	gap: 16px;
}

.examLinks {
	display: flex;
	flex-flow: row wrap;
	justify-content: center;
	background-color: #f2f7fa;
	background-color: var(--color-main--light);
	padding-top: 64px;
}

@media (max-width: 599px) {
	.content {
		gap: 32px;
	}
}
