import React from 'react';

import Layout from '@components/layout';
import ContentWell from '@components/libraryV2/ContentWell';
import ExamButton from '@components/libraryV2/ExamButton';
import FaqBlock from '@components/libraryV2/FaqBlock';
import Heading from '@components/libraryV2/Heading';
import Hero from '@components/libraryV2/Hero';
import ThemeText from '@components/libraryV2/ThemeText';
import FinraConcepts from '@components/resources/concepts-covered-on-finra-exams/FinraConcepts';

import { EXAMS } from '@constants/exams';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';

import styles from './styles.module.scss';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.resourcesConceptsCoveredOnFinraExams,
	description:
		'Table breakdown of the topics covered on the FINRA SIE, Series 6, Series 7, Series 63, Series 65 and Series 66 exams.',
	keywords: 'finra, finra sie, finra series 6, finra series 7, finra series 63, finra series 65, finra series 66',
	title: `FINRA exam coverage (${getCurrentYear()}) | Concepts covered on various FINRA exams`,
});

const FAQ = [
	{
		question: 'Do I need to buy the Series 63 program if I already have access to the Series 65 or 66 programs?',
		answer: (
			<div className={styles.faqItem}>
				<div>
					We recommend you buy the program for each exam you plan on taking. You could possibly avoid buying the Series
					63 program if you&apos;ve bought our Series 65 or 66 programs and cost cutting is a priority, but it&apos;s
					not optimal. The 63 tests state (blue sky) laws and regulations, which is a unit of the 65 and 66. There is no
					material covered on the 63 that isn&apos;t also covered on the 65 and 66.
				</div>
				<div>
					However, each exam tests material in different weightings. For example, the 65 and 66 heavily test investment
					adviser businesses, while the 63 heavily tests broker-dealer businesses. We&apos;ve crafted the final exams
					for each program to provide the closest possible representation of the exam. A person using the 65 or 66
					programs to prepare for the 63 will not have access to practice exams that resemble the actual Series 63 exam.
				</div>
			</div>
		),
	},
	{
		question:
			'Do I need to buy the Series 65 program if I already have access to the Series 66 program (or vice versa)?',
		answer: (
			<div className={styles.faqItem}>
				<div>
					If cost is a significant concern, the Series 65 program can be used to prepare for the Series 66 (or vice
					versa), but it&apos;s not optimal.
				</div>
				<div>
					There&apos;s a great deal of common ground on these two exams - roughly 95-98% of the material overlaps.
					However, some concepts only appear on one of the exams. In particular, the Series 65 covers economic
					principles, business factors, and some options strategies that are not on the Series 66. Additionally, each
					exam presents a different number of questions - 130 on the Series 65 and 100 on the Series 66. Each exam tests
					the material in different weightings as well. For example, the Series 65 allocates 30% of its exam to laws and
					regulations, while the Series 66 allocates 45% to this unit. Therefore, practice exams on one program will not
					closely resemble the other exam.
				</div>
			</div>
		),
	},
	{
		question: "Does the Series 7 include all the material of the Series 6? What does the 6 cover that the 7 doesn't?",
		answer:
			'Virtually all Series 6 test concepts overlap with the Series 7. The 6 tends to go further in depth on investment companies and insurance products, but the 7 covers many more test concepts than the 6.',
	},
];

const ExamLinks = () => {
	const exams = EXAMS.filter((exam) => exam.to.includes('finra'));
	return (
		<div className={styles.examLinks}>
			{exams.map((exam) => (
				<ExamButton key={exam.name} exam={exam} />
			))}
		</div>
	);
};

const Page = () => (
	<Layout secondary>
		<Hero
			title={
				<div>
					<ThemeText>FINRA</ThemeText> exam concepts
				</div>
			}
			description='Detailed breakdown of topics covered on various FINRA exams'
			withBackground
		/>

		<ContentWell tighten>
			<div className={styles.content}>
				<div className={styles.description}>
					<div>
						Successfully preparing for a FINRA or NASAA exam involves meticulously preparing for seemingly endless
						questions on dozens of testable concepts. Within each concept, hundreds of test points can exist. Many test
						takers begin their studies with little-to-no understanding of the broad topics their exam covers. The
						uncertainty can manifest in frustration and test anxiety, neither of which are productive for studying.
					</div>
					<div>
						Good news - Achievable has you covered! Our programs provide industry-best preparation tools for securities
						licensing exams, and we&apos;ve also created a visual guide for the topics covered on each exam. You&apos;ll
						find a list of 30 common testable concepts and their relevant &quot;testability.&quot; We&apos;ve
						color-coded each concept to reflect a range between non-testable and heavily tested.
					</div>
				</div>

				<FinraConcepts />
			</div>

			<ExamLinks />
		</ContentWell>

		<ContentWell withBackground>
			<Heading size='h2' tagType='h2'>
				Frequently <ThemeText>asked</ThemeText> questions
			</Heading>
			<FaqBlock className={styles.faq} items={FAQ} />
		</ContentWell>
	</Layout>
);

export default Page;
