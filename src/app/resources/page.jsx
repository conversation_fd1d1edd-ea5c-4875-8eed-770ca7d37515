
import Layout from '@components/layout';
import ContentWell from '@components/libraryV2/ContentWell';
import Hero from '@components/libraryV2/Hero';
import ThemeText from '@components/libraryV2/ThemeText';
import ResourceSection from '@components/resources/ResourceSection';

import { ACT_RESOURCE_PAGES } from '@constants/resources/act';
import { CLT_RESOURCE_PAGES } from '@constants/resources/clt';
import { FINRA_RESOURCE_PAGES } from '@constants/resources/finra';
import { GRE_RESOURCE_PAGES } from '@constants/resources/gre';
import { MISCELLANEOUS_RESOURCE_PAGES } from '@constants/resources/misc';
import { STUDENT_RESOURCE_PAGES } from '@constants/resources/student';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

import styles from './styles.module.scss';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.resources,
	description: 'Resources for FINRA, GRE, and ACT exams, and university admissions.',
	keywords:
		'finra, finra sie, finra series 6, finra series 7, finra series 63, finra series 65, finra series 66, gre, act, university admissions, college admissions',
	title: 'Achievable resources | Resources for FINRA, GRE, and ACT exams, and university admissions',
});

const Resources = () => (
	<Layout>
		<Hero
			title={
				<div className={styles.title}>
					<ThemeText>Achievable</ThemeText> <div>Resources</div>
				</div>
			}
			withBackground
		/>

		<ContentWell size='dog'>
			<div className={styles.sections}>
				<ResourceSection className='section' title='FINRA' resources={FINRA_RESOURCE_PAGES} />
				<ResourceSection className='section' title='GRE' resources={GRE_RESOURCE_PAGES} />
				<ResourceSection className='section' title='ACT' resources={ACT_RESOURCE_PAGES} />
				<ResourceSection className='section' title='CLT' resources={CLT_RESOURCE_PAGES} />
				<ResourceSection className='section' title='Student' resources={STUDENT_RESOURCE_PAGES} />
				<ResourceSection className='section' title='Miscellaneous' resources={MISCELLANEOUS_RESOURCE_PAGES} />
			</div>
		</ContentWell>
	</Layout>
);

export default Resources;
