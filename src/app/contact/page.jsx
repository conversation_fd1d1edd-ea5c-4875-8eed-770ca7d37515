import ContactPage from '@pages/ContactPage';

import QUOTES from '@constants/quotes';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { shuffle } from '@lib/helpers';

const SHORT_QUOTES = QUOTES.filter(({ quote }) => quote.length < 150);

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.contact,
	description: "Have a question that wasn't answered by our site? Send a message here to the Achievable team.",
	keywords: 'achievable, achievable sie, achievable gre, achievable usmle, achievable series 7',
	title: 'Contact us',
});

const Page = () => {
	const [quote] = shuffle(SHORT_QUOTES);

	return <ContactPage quote={quote} />;
};

export default Page;
