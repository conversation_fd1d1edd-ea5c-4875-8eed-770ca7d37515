import React from 'react';

import CaseStudy from '@components/enterprise/CaseStudy';
import EmpoweringInstructors from '@components/enterprise/EmpoweringInstructors';
import Layout from '@components/layout';
import Hero from '@components/libraryV2/Hero';
import ThemeText from '@components/libraryV2/ThemeText';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

import styles from './styles.module.scss';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.enterprise,
	description:
		"Achievable's Enterprise training programs are proven to improve student success at scale with low time investments.",
	keywords:
		'Financial advisor training, financial advisor training program, SIE training, SIE training program, FINRA training, FINRA training program, FINRA SIE training, FINRA SIE training program',
	title: 'Enterprise training programs',
});

const Enterprise = () => {
	return (
		<Layout secondary>
			<Hero
				className={styles.hero}
				title={
					<div>
						<ThemeText>Achievable&apos;s</ThemeText> <div>Enterprise training programs</div>
					</div>
				}
				description={<div className={styles.description}>Proven impact on student success at scale</div>}
				withBackground
			/>

			<div className={styles.content}>
				<EmpoweringInstructors />

				<CaseStudy />
			</div>
		</Layout>
	);
};

export default Enterprise;
