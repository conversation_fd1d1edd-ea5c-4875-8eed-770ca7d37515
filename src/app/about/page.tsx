import BrandJson from '@components/about/BrandJson';
import CompanyOverview from '@components/about/CompanyOverview';
import StudyExperience from '@components/about/StudyExperience';
import Team from '@components/about/Team';
import ValuesSection from '@components/about/ValuesSection';
import Layout from '@components/layout';
import Hero from '@components/libraryV2/Hero';
import ThemeText from '@components/libraryV2/ThemeText';

import { SCHEMA_TYPES } from '@constants/seoSchema';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import { generateWebPageSchema } from '@lib/seoSchema/helpers';

import styles from './styles.module.scss';

const seo = {
	canonical: CANONICAL_URLS.about,
	title: 'About Achievable and our effective, approachable, and affordable exam prep',
	description:
		"About Achievable and why we are building no-BS exam prep for today's college students and young professionals - mobile, guided, and individually optimized.",
	keywords:
		'achievable, exam prep, exam prep, FINRA SIE, FINRA series 7, gre, graduate record exam, usmle,  United States Medical Licensing Examination',
};

const { title, description, canonical } = seo;

export const metadata = generatePageMetadata(seo);

const schema = generateWebPageSchema({
	type: SCHEMA_TYPES.AboutPage,
	title,
	description,
	canonical,
});

const About = () => (
	<Layout jsonLd={{ schemas: [schema] }}>
		<Hero
			title={
				<div className={styles.title}>
					<div>We believe</div>
					<ThemeText>anyone can pass</ThemeText>
				</div>
			}
			description='A small team reimagining education with smart technology.'
			reverseBackground
			withBackground
		/>

		<CompanyOverview />

		<ValuesSection />

		<StudyExperience />

		<Team />

		<BrandJson />
	</Layout>
);

export default About;
