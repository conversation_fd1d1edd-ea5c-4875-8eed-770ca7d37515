import Layout from '@components/layout';
import HeroStretch from '@components/libraryV2/HeroStretch';
import PassGuaranteedCta from '@components/libraryV2/PassGuaranteedCta';
import SectionTitle from '@components/libraryV2/SectionTitle';
import ThemeText from '@components/libraryV2/ThemeText';
import Achievers from '@components/reviews/Achievers';
import Quotes from '@components/reviews/Quotes';

import { SCHEMA_ACHIEVABLE_REVIEWS } from '@/lib/seoSchema/shared';
import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getQuotes from '@lib/getQuotes';

import styles from './styles.module.scss';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.reviews,
	description:
		"Thousands of people love Achievable - but don't just take our word for it. Hear what real Achievable customers have to say.",
	keywords: 'achievable, achievable sie, achievable gre, achievable usmle, achievable series 7',
	title: 'Reviews and ratings',
});

const MeetTheAchievers = () => {
	const quotes = getQuotes();

	return (
		<Layout jsonLd={{ schemas: SCHEMA_ACHIEVABLE_REVIEWS }}>
			<HeroStretch
				backgroundSize='medium'
				title={
					<>
						Meet the <ThemeText>Achievers</ThemeText>
					</>
				}
				titleTag='h1'
				description='A collection of short interviews with people using Achievable to pass
                their exams and advance their careers'
				hideBackgroundMobile
			>
				<Achievers />
				<div className={styles.reviews}>
					<SectionTitle
						description="Students love us - but don't just take our word for it. Hear what real users have to say about Achievable."
						title={
							<>
								<ThemeText>Achievable</ThemeText> Reviews
							</>
						}
						size='h2'
						tagType='h2'
					/>
				</div>
			</HeroStretch>

			<Quotes quotes={quotes} />

			<PassGuaranteedCta />
		</Layout>
	);
};

export default MeetTheAchievers;
