import Logos from '@components/company/Logos';
import { SidebarDesktop, SidebarMobile } from '@components/company/Sidebar';
import Layout from '@components/layout';
import Hero from '@components/libraryV2/Hero';
import ThemeText from '@components/libraryV2/ThemeText';

import generatePageMetadata from '@lib/generatePageMetadata';

export const metadata = generatePageMetadata({
	canonical: '/company/logo/',
	description: 'Download Achievable logos in various formats and sizes. View brand guidelines and usage instructions.',
	title: 'Achievable Logo Downloads & Brand Guidelines',
});

const LogoPage = () => {
	return (
		<Layout>
			<SidebarMobile />

			<Hero
				description='Download Achievable logos in various formats! Please do not alter the original files with filters or effects.'
				title={
					<>
						<ThemeText>Achievable&apos;s</ThemeText> logo
					</>
				}
				withBackground
			/>

			<SidebarDesktop>
				<Logos />
			</SidebarDesktop>
		</Layout>
	);
};

export default LogoPage;
