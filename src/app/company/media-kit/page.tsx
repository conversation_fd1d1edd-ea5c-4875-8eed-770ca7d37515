import AboutSection from '@components/company/MediaKit/about';
import LogoSection from '@components/company/MediaKit/logo';
import MediaContactSection from '@components/company/MediaKit/media-contact';
import ScreenshotsSection from '@components/company/MediaKit/screenshots';
import { SidebarDesktop, SidebarMobile } from '@components/company/Sidebar';
import Layout from '@components/layout';
import Hero from '@components/libraryV2/Hero';
import ThemeText from '@components/libraryV2/ThemeText';

import generatePageMetadata from '@lib/generatePageMetadata';

export const metadata = generatePageMetadata({
	canonical: '/company/media-kit/',
	description: 'Download Achievable logos, screenshots, and media resources. Contact us for media inquiries.',
	title: 'Achievable Media Kit - Logos, Screenshots & Press Resources',
});

const MediaKitPage = () => (
	<Layout>
		<SidebarMobile />

		<Hero
			description='Download Achievable logos, screenshots, and media resources. Contact us for media inquiries.'
			title={
				<>
					<ThemeText>Achievable&apos;s</ThemeText> media kit
				</>
			}
			withBackground
			reverseBackground
		/>

		<SidebarDesktop>
			<LogoSection />

			<AboutSection />

			<ScreenshotsSection />

			<MediaContactSection />
		</SidebarDesktop>
	</Layout>
);

export default MediaKitPage;
