import { notFound } from 'next/navigation';

import AchieversLayout from '@components/reviews/AchieversLayout';

import achievers from '@constants/meet-the-achievers';
import generatePageMetadata from '@lib/generatePageMetadata';

export async function generateMetadata({ params }) {
	const { slug: achieverSlug } = await params;
	const achiever = achievers[achieverSlug];

	if (!achiever) return notFound();

	const { seo } = achiever;
	const { title, description, canonical } = seo;

	return generatePageMetadata({
		title,
		description,
		canonical,
		keywords: 'achievable, achievable sie, achievable gre, achievable usmle, achievable series 7',
	});
}

export function generateStaticParams() {
	const params = Object.keys(achievers).map((slug) => ({ slug }));
	return params;
}

const Page = async ({ params }) => {
	const { slug: achieverSlug } = await params;

	const achiever = achievers[achieverSlug];
	if (!achiever) {
		return notFound();
	}

	const { info, name, seo, image, slug } = achiever;

	return <AchieversLayout info={info} name={name} seo={seo} slug={slug} Image={image} />;
};

export default Page;
