import PromoCodesPage from '@components/pages/PromoCodesPage';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';
import getCurrentYear from '@lib/getCurrentYear';

export const generateMetadata = async () => {
	const today = new Date();
	const monthYear = today.toLocaleString('default', { month: 'long', year: 'numeric' });
	const metadata = generatePageMetadata({
		canonical: CANONICAL_URLS.officialAchievablePromoCodes,
		description: `The ONLY official source for Achievable partner, student, and service discounts. Don't fall for scams - these are the real ${getCurrentYear()} Achievable deals, promos, and offers.`,
		keywords: 'achievable promo code, achievable discount code',
		title: `Official Achievable promo codes (${monthYear})`,
	});

	return metadata;
};

const Page = () => <PromoCodesPage />;

export default Page;
