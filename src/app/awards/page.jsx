import AwardsPage from '@components/pages/AwardsPage';

import { CANONICAL_URLS } from '@constants/siteMetadata';
import generatePageMetadata from '@lib/generatePageMetadata';

export const metadata = generatePageMetadata({
	canonical: CANONICAL_URLS.awards,
	description:
		'At Achievable, we honor educators, tutors, and admissions counselors who have exemplified excellence, innovation, and devotion to fostering knowledge and success.',
	keywords:
		'achievable, achievable awards, excellence in education, excellence in education awards, best tutor, best teacher, best counselor, edtech company, edtech companies, education award, honors of excellence',
	title: 'Achievable awards',
});

const Page = () => <AwardsPage />;

export default Page;
