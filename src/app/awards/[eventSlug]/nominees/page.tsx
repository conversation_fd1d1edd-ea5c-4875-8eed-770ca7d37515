import Nominees from '@components/awards/Nominees';

import { AwardEventsEnum } from '@constants/awards/events';
import { generateAwardsPageMetadata, handleAwardsPage } from '@handlers/handleAwardsPage';

export const revalidate = 86400;

interface Params {
	params: Promise<{ eventSlug: string }>;
}

export const generateMetadata = async ({ params }: Params) => {
	const { eventSlug } = await params;
	const metadata = await generateAwardsPageMetadata({ eventSlug });
	return metadata;
};

export function generateStaticParams() {
	const params = Object.keys(AwardEventsEnum).map((slug) => ({ eventSlug: slug.toLowerCase() }));
	return params;
}

const Page = async ({ params }: Params) => {
	const { eventSlug } = await params;
	const { event, nomineesByCategory, winnerUuidByCategory } = await handleAwardsPage({ eventSlug });
	return <Nominees event={event} nomineesByCategory={nomineesByCategory} winnerUuidByCategory={winnerUuidByCategory} />;
};

export default Page;
