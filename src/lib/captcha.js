import envConfig from '@constants/envConfig';
import { isBot } from '@lib/userAgent';

const { isProduction, RECAPTCHA_SITE_KEY } = envConfig;

export const ensureCaptchaLoaded = () => {
	if (!isBot() && isProduction) {
		if (window.grecaptcha) {
			return;
		}

		window.grecaptcha = {};
		window.grecaptcha.ready = (cb) => {
			// window.__grecaptcha_cfg is a global variable that stores reCAPTCHA's
			// configuration. By default, any functions listed in its 'fns' property
			// are automatically executed when reCAPTCHA loads.
			const c = '___grecaptcha_cfg';
			window[c] = window[c] || {};
			(window[c]['fns'] = window[c]['fns'] || []).push(cb);
		};

		const script = document.createElement('script');
		script.id = `recaptcha_${RECAPTCHA_SITE_KEY}`;
		script.src = `https://www.google.com/recaptcha/api.js?render=${RECAPTCHA_SITE_KEY}`;
		document.head.appendChild(script);
	}
};

export const getCaptchaToken = async ({ action }) => {
	if (!isBot() && isProduction) {
		return new Promise((resolve, reject) => {
			window.grecaptcha.ready(() => {
				window.grecaptcha.execute(RECAPTCHA_SITE_KEY, { action }).then((token) => {
					resolve(token);
				}, reject);
			});
		});
	}
};
