/** @type {import('next').NextConfig} */
import path from 'path';
import { fileURLToPath } from 'url';

import withBundleAnalyzer from '@next/bundle-analyzer';
import type { NextConfig } from 'next';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const nextConfig: NextConfig = {
	trailingSlash: true,
	sassOptions: {
		includePaths: [path.join(__dirname, 'styles')],
	},
	async redirects() {
		return [
			{
				source: '/cookie',
				destination: 'https://www.iubenda.com/privacy-policy/18707085/cookie-policy',
				permanent: false,
			},
			{
				source: '/privacy',
				destination: 'https://www.iubenda.com/privacy-policy/18707085/full-legal',
				permanent: false,
			},
			{
				source: '/terms',
				destination: 'https://www.iubenda.com/terms-and-conditions/18707085',
				permanent: false,
			},
			{
				source: '/redeem',
				destination: 'https://app.achievable.me/redeem',
				permanent: false,
			},
			{
				source: '/enroll/:splat*',
				destination: 'https://app.achievable.me/enroll/:splat*',
				permanent: false,
			},
			{
				source: '/exams/act/resources/',
				destination: '/exams/act/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/finra-sie/resources/',
				destination: '/exams/finra-sie/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/finra-series-6/resources/',
				destination: '/exams/finra-series-6/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/finra-series-63/resources/',
				destination: '/exams/finra-series-63/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/finra-series-65/resources/',
				destination: '/exams/finra-series-65/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/finra-series-66/resources/',
				destination: '/exams/finra-series-66/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/gre/resources/',
				destination: '/exams/gre/overview/#resources',
				permanent: true,
			},
			{
				source: '/exams/clt/overview/',
				destination: '/exams/clt/prepare/',
				permanent: true,
			},
			{
				source: '/exams/finra-series-7/overview/',
				destination: '/exams/finra-series-7/prepare/',
				permanent: true,
			},
			{
				source: '/company/about/',
				destination: '/about/',
				permanent: true,
			},
		];
	},
	webpack(config) {
		// @ts-ignore
		const fileLoaderRule = config.module.rules.find((rule) => rule.test?.test?.('.svg'));

		config.module.rules.push(
			{
				...fileLoaderRule,
				test: /\.svg$/i,
				resourceQuery: /url/, // *.svg?url
			},
			{
				test: /\.svg$/i,
				issuer: fileLoaderRule.issuer,
				resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
				use: ['@svgr/webpack'],
			}
		);

		fileLoaderRule.exclude = /\.svg$/i;

		return config;
	},
};

const bundleAnalyzer = withBundleAnalyzer({
	enabled: process.env.ANALYZE === 'true',
});

export default bundleAnalyzer(nextConfig);
