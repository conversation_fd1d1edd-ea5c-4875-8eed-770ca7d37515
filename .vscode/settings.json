{"editor.insertSpaces": false, "editor.tabSize": 2, "editor.detectIndentation": false, "editor.formatOnSave": true, "editor.formatOnPaste": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "files.eol": "\n", "files.trimTrailingWhitespace": true, "files.insertFinalNewline": true, "files.trimFinalNewlines": false, "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "prettier.requireConfig": true, "prettier.useEditorConfig": true, "typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "javascript.suggest.autoImports": true, "editor.rulers": [120], "editor.wordWrap": "wordWrapColumn", "editor.wordWrapColumn": 120, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/coverage": true, "**/.next": true, "**/yarn.lock": true, "**/package-lock.json": true}, "files.watcherExclude": {"**/node_modules/**": true, "**/dist/**": true, "**/build/**": true, "**/.next/**": true}}