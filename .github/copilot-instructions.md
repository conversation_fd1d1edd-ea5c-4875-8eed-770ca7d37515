This is a Node.js and Gatsby based repository for Achievable's marketing website. It serves as the primary public-facing site for Achievable, an exam prep platform. Please follow these guidelines when contributing:

## Code Standards

### Required Before Each Commit

- Run `yarn format` before committing changes to ensure proper code formatting
- Run Prettier on all supported file types to maintain consistent style
- Run `yarn lint` and ensure there are no ESLint warnings or errors
- All files should end with a newline

### Development Flow

- Install dependencies: `yarn install`
- Development server: `yarn develop` (runs Gatsby in development mode)
- Build: `yarn build` (creates production-ready static site)
- Serve built site locally: `yarn serve`
- Format code: `yarn format`

## Repository Structure

- `src/components/`: Reusable React components
- `src/constants/`: Configuration and constant values
- `src/images/`: Image assets used throughout the site
- `src/lib/`: Utility functions and shared logic
- `src/pages/`: Page components that map to URLs
- `src/queries/`: GraphQL queries
- `src/svg/`: SVG assets
- `src/styles/`: SASS styles
- `src/blog/`: Blog content
- `src/directory/`: Directory content for tutors and counselors
- `scripts/`: Helper scripts for various tasks
- `static/`: Static assets served directly without processing

## Key Guidelines

1. Follow React and JavaScript best practices and idiomatic patterns
2. Maintain existing component and styling structure
3. Use path aliases (e.g., @components, @constants) as defined in gatsby-config.js
4. Follow the existing styling approach using SASS
5. When adding new pages, ensure they follow the existing pattern in src/pages
6. Keep accessibility in mind when implementing UI components
7. Optimize images and other assets for web performance
8. Follow semantic HTML practices for better SEO and accessibility
9. The project uses yarn, not npm; do not commit a package-lock.json file
10. **Lockfile Policy**: Do not change lockfiles (yarn.lock, package-lock.json) unless explicitly requested or when packages are changed. Lockfiles should only be modified when dependencies are added, removed, or updated intentionally.
