name: 'Copilot Setup Steps'

# Allow testing of the setup steps from your repository's "Actions" tab.
on: workflow_dispatch

jobs:
  # The job MUST be called copilot-setup-steps or it will not be picked up by Copilot.
  copilot-setup-steps:
    runs-on: ubuntu-latest

    # Set the permissions to the lowest permissions possible needed for your steps.
    permissions:
      # Read access to repository content is needed to checkout the code
      contents: read

    # Set up steps for the Copilot agent
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install JavaScript dependencies
        # Using --legacy-peer-deps as recommended in README.md
        run: npm install --legacy-peer-deps
        env:
          GITHUB_TOKEN: ${{ secrets.ACHIEVABLE_GITHUB_TOKEN }}
          FONT_AWESOME_TOKEN: ${{ secrets.FONT_AWESOME_TOKEN }}

      - name: Verify setup
        run: |
          echo "Node.js version:"
          node --version
          echo "npm version:"
          npm --version
          echo "Environment setup complete"
