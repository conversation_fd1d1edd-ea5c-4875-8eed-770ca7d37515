# File Structure Conventions

## Root Directory Structure

```
src/
├── app/                # Next.js App Router pages
├── components/         # Reusable React components along with page-specific & achievable/ux components wrappers
├── constants/          # Configuration and constant values
├── data/               # Local data files (json)
├── handlers/           # Shared data handlers
├── lib/                # Utility functions and shared logic
├── styles/             # Global SASS styles
├── svg/                # SVG assets
├── utils/              # Utility functions
├── types/              # TypeScript types
├── images/             # Image assets
├── public/             # Static assets served directly without processing
```

## Page Structure

### App Router Pages

Pages are organized in the `src/app` directory following the Next.js App Router conventions:

```
src/app/
├── page.tsx            					# Home page (static)
├── about/
│   └── page.tsx       						# About page (static)
├── exams/
│   ├── act/
│   │   ├── overview/
│   │   │   └── page.tsx  				# ACT overview page
│   │   ├── free-practice-exam/
│   │   │   └── page.tsx  				# ACT free practice exam page
│   │   └── ...           				# More ACT pages
│   ├── clt/
│   │   ├── prepare/
│   │   │   └── page.tsx  				# CLT prepare page
│   │   ├── exam-info/
│   │   │   └── page.tsx  				# CLT exam info page
│   │   ├── faq/
│   │   │   └── page.tsx  				# CLT FAQ page
│   │   └── ...           				# More CLT pages
│   └── ...               				# More exam types
├── directory/
│   └── [slug]/          					# Dynamic directory page
│       └── [[...page]]/  				# Dynamic directory page with pagination
│           └── page.tsx  				# Directory page
├── articles/
│   │── finance-jobs/
│		│		│── [slug]/
│		│		│		└── page.tsx    			# Article page (individual)
│		│		└── national/
│		│				└── page.tsx   				# Article page (aggregated)
│   └── ...
├── meet-the-achievers/
│   └── [slug]/
│       └── page.tsx    					# Meet the achievers page
├── not-found.tsx       					# 404 page
└── ...                 					# More pages
```

### Naming Conventions

- Use kebab-case for directory and file names
- Dynamic routes use square brackets: `[slug]`
- Optional catch-all routes use double brackets: `[[...page]]`

### Component Conventions

- Reusable components go in the `libraryV2` directory
- Page-specific components go in a folder with the same name as the page
- Each component should have its own directory with index file
- Use SASS modules for styling (`.module.scss`)

## Image Conventions

### Static Images

```
src/
└── images/
    ├── exams/
    │   ├── act/
    │   │   ├── act-dashboard--896.png
    │   │   ├── act-reading--896w.png
    │   │   └── ...
    │   ├── clt/
    │   │   ├── clt-dashboard--896.png
    │   │   ├── clt-prepare--896w.png
    │   │   └── ...
    │   └── ...
    └── ...

public/
└── images/
    ├── awards/
    │   ├── accepted.webp
    │   └── ...
    └── ...

```

### Image Usage

- For all images, use `NextImage` component which is a wrapper around `next/image`
- For static images, import from `src/images` directory and optionally pass custom sizing and styling props
- For dynamic images, add absolute reference from `public/images` directory directly into `src` and make sure to pass `width` and `height` prop manually
- SVGs should be stored in `/svg` directory and imported as components
- Optimize all images for web performance using `scripts/optimize-images.js` script
- For specifying dynamic images (images in the data json files), reference them as objects with `src` and `width`/`height` properties. The `src` should be the absolute path from `public/images` directory. For example:
  ```json
  {
  	"src": "/images/exams/act/act-dashboard--896.png",
  	"width": 896,
  	"height": 448
  }
  ```

## Tips

- Use path aliases (e.g., `@components`, `@constants`) as defined in configuration
