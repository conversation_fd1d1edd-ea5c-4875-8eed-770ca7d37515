# Checklists

## 1. New page

- [ ] Create the page in the appropriate directory
- [ ] Add the `CANONICAL_URLS` constant in `@constants/siteMetadata` if it's a top-level page
- [ ] Add the handler function in `@handlers` if it's a dynamic page
- [ ] If the page is dynmaic appropriately use `generateStaticParams` and `revalidate` to generate required paths at build time
- [ ] Ensure page is added to the sitemap. If static, adding to canonical urls is enough. If dynamic, add the handler function to `handleSitemap.ts`
- [ ] Look for hydration errors.
- [ ] Run `yarn build` and observe the page is generated correctly
- [ ] Run `yarn start` and observe the page is rendered correctly

## 2. Images

- [ ] Optimize images - preferably use `scripts/optimize-images.js`
- [ ] Check for SVG sizing issues and try adding viewbox rather than CSS styles to prevent overflowing of certain SVGs with predefined width or height.
- [ ] If the image is component or page specific place it next to component itself. If it's shared among few components or pages, place it in `@images` directory.
- [ ] If the image is referenced in dynamic data/pages ppreferably add it to the public directory and reference it as object in data file along with width and height.

## 3. SEO

- [ ] Add metadata and structured data (JSON-LD) to the page
- [ ] Verify canonical URL is correct
- [ ] Verify title, description, and keywords are correct and not defaults
- [ ] Verify structured data is correct and complete. You can use [Google's Structured Data Testing Tool](https://search.google.com/structured-data/testing-tool) to test.
