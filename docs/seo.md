# SEO Implementation Guide

## SEO Strategy

Our SEO strategy focuses on:

- Proper metadata for all pages
- Structured data (JSON-LD)
- Canonical URLs

## Metadata Implementation

### Using `generatePageMetadata`

We use a centralized utility function `generatePageMetadata` to maintain consistent metadata across the site:

```typescript
// Example usage in a page file
import generatePageMetadata from '@lib/generatePageMetadata';

export const metadata = generatePageMetadata({
	title: 'Page Title',
	description: 'Page description for SEO',
	keywords: 'keyword1, keyword2, keyword3',
	canonical: '/path/to/page/',
});
```

### Dynamic Metadata with `generateMetadata`

For pages with dynamic routes, use the `generateMetadata` function:

```typescript
export const generateMetadata = async ({ params }: Params) => {
	const { slug } = await params;
	const pageData = await fetchPageData(slug);

	return generatePageMetadata({
		title: pageData.title,
		description: pageData.description,
		keywords: pageData.keywords,
		canonical: `/path/to/${slug}/`,
	});
};
```

## Structured Data (JSON-LD)

We use helper functions to generate structured data:

```typescript
import { generateCourseWebPageSchema } from '@lib/seoSchema/helpers';

// In your page component
const schemas = generateCourseWebPageSchema({
  sku: 'product-sku',
  canonical: '/path/to/page/',
});

// Then in your layout component
<Layout jsonLd={{ schemas }}>
  {/* Page content */}
</Layout>
```

### Common Schema Types

- `WebPage` - Basic schema for all pages
- `Course` - For exam prep product pages
- `Article` - For blog/article pages
- `FAQPage` - For FAQ sections
- `Organization` - For company pages

## Open Graph and Twitter Cards

Open Graph and Twitter card metadata is automatically generated from the metadata you provide:

```typescript
export const metadata = generatePageMetadata({
	title: 'Page Title',
	description: 'Page description',
	image: {
		url: 'https://example.com/image.jpg',
		width: 640,
		height: 640,
	},
});
```

This generates both Open Graph and Twitter card metadata.

## Canonical URLs

Always specify canonical URLs to prevent duplicate content issues:

```typescript
export const metadata = generatePageMetadata({
	// ...other metadata
	canonical: '/path/to/page/',
});
```

For all static pages like product pages, use constants from `@constants/siteMetadata`:

```typescript
import { CANONICAL_URLS } from '@constants/siteMetadata';

export const metadata = generatePageMetadata({
	// ...other metadata
	canonical: CANONICAL_URLS.examsSeries65Overview,
});
```

## Sitemap Implementation

We generate a sitemap for our website to help search engines index our pages. The sitemap is automatically generated and updated whenever new content is added.

### Sitemap Generation

The sitemap is generated using functions.

- For all static pages, we use a `CANONICAL_URLS` constant. Using that we generate the sitemap entries.
- For dynamic pages, we use a handler function to fetch the specific data and generate the sitemap entries.

Here's how it works:

```typescript
const createStaticRoutes = () => {
	return Object.values(CANONICAL_URLS).map((slug) => getSitemapRoute({ slug }));
};

const createTutorRoutes = async (product: string) => {
	if (EXCLUDED_COURSES.includes(product)) {
		return [];
	}

	const routes = [];
	const basePath = `/directory/${product}-tutors`;

	routes.push(getSitemapRoute({ slug: basePath }));

	const productData = await getTutorProductData(product);
	const tutors = productData?.tutors?.map((tutor: any) => tutor?.uuid) || [];

	routes.push(...createPaginationRoutes(basePath, tutors.length));

	tutors.forEach((tutorId: string) => {
		routes.push(getSitemapRoute({ slug: `${basePath}/${tutorId}` }));
	});

	return routes;
};
```
