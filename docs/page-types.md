# 📘 Page Types Guide

This guide outlines the different types of pages on our Next.js marketing site, including how they're implemented, rendered, and optimized.

---

## 1. Static Pages

These pages are fully static and generated at build time.

- **Examples**: Home page (`src/app/page.tsx`), exam overview pages (`src/app/exams/act/overview/page.tsx`, etc.)
- **Features**:
  - Rendered statically with zero runtime overhead
  - Use `generateMetadata() or export const metadata` for static SEO metadata
  - Include static JSON-LD structured data

---

## 2. Directory Pages

These pages list items like tutors, counselors, etc. They are structured under dynamic route folders:

```
src/app/directory/[slug]/[[...page]]/
```

### Behavior

- Some pages are **partially static** using `generateStaticParams()` and `revalidate`
- Pagination and profile pages are handled dynamically
- All data is sourced from local `.json` files from data folder
- Data is read through **handlers** wrapped in `unstable_cache()` for cached performance

### Example Handler Usage

```ts
const { counselors, meta } = await getCounselorTypeData(typeSlug);
```

- These handlers:

  - Load JSON from file system
  - Return paginated data, profile-level data, and metadata
  - Return 404s for invalid states using `notFound()`

### Metadata

Metadata is dynamically generated based on page context (e.g., list vs profile):

```ts
export async function generateMetadata({ params }: Params) {
	const metadata = await generateCounselorsPageMetadata({
		typeSlug: 'undergraduate-admissions',
		page: params.page,
	});
	return metadata;
}
```

### Features

- Revalidated daily: `export const revalidate = 86400`
- SEO-friendly metadata and canonical URLs
- JSON-LD structured data tailored per list or profile page
- Uses `LIMIT` for pagination
- Routes like `/directory/[type]-counselors/[uuid]`

---

## Article Pages

- Located at `/articles/[slug]/`
- First pages like `/national` are pre-generated using `generateStaticParams()`
- Others fall back to dynamic rendering (ISR - generated on first visit then cached until revalidation window is hit)
- Metadata and JSON-LD are derived from markdown or content metadata

---

### Performance

- File data is read using handlers with `unstable_cache()` to boost performance:

```ts
import { unstable_cache } from 'next/cache';

import CacheKeys from '@lib/server/CacheKeys';

export const getCounselorTypeData = unstable_cache(
	async (typeSlug) => {
		const raw = await fs.readFile(`data/${typeSlug}.json`, 'utf8');
		return JSON.parse(raw);
	},
	[CacheKeys.getCounselorTypeData],
	{
		tags: [CacheKeys.getCounselorTypeData],
	}
);
```

### Handlers

Each dynamic section has a handler (e.g. `handleCounselorsPage.ts`) that:

- Loads data from disk
- Returns paginated or profile data
- Generates metadata dynamically
- Handles 404s with `notFound()`

---

## Implementation Conventions

- Reuse layout component (`@components/layout`) on each page
- Use `generateMetadata() or export const metadata` on every page
- Use `generateStaticParams()` with `revalidate` for frequently accessed dynamic routes
- Wrap file reads in `unstable_cache()` for better performance
- Always add checks for invalid states/paths/params and use `notFound()`
- Include canonical URLs, especially for paginated routes
- Add structured data via JSON-LD

---

## Other pages/routes

- ### Not found page (`src/app/not-found.tsx`)

  - Renders 404 page
  - No SEO metadata or structured data
  - Statically generated

- ### GMC Product Feed XML

  - `src/app/gmc-product-feed[0].xml/route.ts`
  - `src/app/gmc-product-feed.xml/route.ts`
  - Force-static at builds, no revalidation
  - Generated from `handleGmcFeed.ts`

- ### Sitemap
  - `src/app/sitemap.xml/route.ts`
  - Force-static at builds, no revalidation
  - Generated from `handleSitemap.ts`
